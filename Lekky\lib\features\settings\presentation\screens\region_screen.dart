import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../controllers/settings_controller.dart';
import '../../../../features/setup/presentation/widgets/region_settings_card.dart';

/// Region settings screen
class RegionScreen extends StatelessWidget {
  /// Constructor
  const RegionScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Region Settings'),
        backgroundColor: const Color(0xFF424242),
        foregroundColor: Colors.white,
      ),
      body: Consumer<SettingsController>(
        builder: (context, controller, _) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Region settings card
                RegionSettingsCard(
                  language: controller.language,
                  currency: controller.currency,
                  currencySymbol: controller.currencySymbol,
                  onLanguageChanged: (language) {
                    controller.updateLanguage(language);
                  },
                  onCurrencyChanged: (currency, symbol) {
                    controller.updateCurrency(currency, symbol);
                  },
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
