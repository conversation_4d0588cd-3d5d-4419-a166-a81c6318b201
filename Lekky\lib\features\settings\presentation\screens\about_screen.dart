import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../core/widgets/lekky_button.dart';
import '../controllers/settings_controller.dart';

/// About screen
class AboutScreen extends StatelessWidget {
  /// Constructor
  const AboutScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('About'),
        backgroundColor: const Color(0xFF424242),
        foregroundColor: Colors.white,
      ),
      body: Consumer<SettingsController>(
        builder: (context, controller, _) {
          return ListView(
            padding: const EdgeInsets.all(16.0),
            children: [
              // App Info section
              Card(
                margin: const EdgeInsets.only(bottom: 16.0),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Row(
                        children: [
                          Icon(Icons.info, color: Colors.blue),
                          SizedBox(width: 16),
                          Text(
                            'App Information',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),

                      // App logo
                      Center(
                        child: Container(
                          width: 100,
                          height: 100,
                          decoration: BoxDecoration(
                            color: Colors.blue,
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: const Icon(
                            Icons.electric_meter,
                            size: 60,
                            color: Colors.white,
                          ),
                        ),
                      ),

                      const SizedBox(height: 16),

                      // App name and version
                      const Center(
                        child: Text(
                          'Lekky',
                          style: TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      const Center(
                        child: Text(
                          'Version 1.0.0',
                          style: TextStyle(
                            fontSize: 16,
                          ),
                        ),
                      ),

                      const SizedBox(height: 16),

                      // App description
                      const Text(
                        'Lekky is a prepaid electricity meter tracking app designed to help you monitor your electricity usage and manage your meter readings.',
                        style: TextStyle(
                          fontSize: 14,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),

              // Features section
              Card(
                margin: const EdgeInsets.only(bottom: 16.0),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Row(
                        children: [
                          Icon(Icons.star, color: Colors.amber),
                          SizedBox(width: 16),
                          Text(
                            'Features',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),

                      // Features list
                      _buildFeatureItem(
                        Icons.track_changes,
                        'Track Meter Readings',
                        'Record and monitor your meter readings over time',
                      ),
                      _buildFeatureItem(
                        Icons.add_card,
                        'Record Top-ups',
                        'Keep track of all your meter top-ups',
                      ),
                      _buildFeatureItem(
                        Icons.calculate,
                        'Usage Calculations',
                        'Automatically calculate your daily usage',
                      ),
                      _buildFeatureItem(
                        Icons.notifications,
                        'Smart Notifications',
                        'Get alerts when your balance is running low',
                      ),
                      _buildFeatureItem(
                        Icons.history,
                        'History Tracking',
                        'View your complete usage history',
                      ),
                      _buildFeatureItem(
                        Icons.backup,
                        'Data Backup',
                        'Backup and restore your data',
                      ),
                    ],
                  ),
                ),
              ),

              // Tips & Tricks section
              Card(
                margin: const EdgeInsets.only(bottom: 16.0),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Row(
                        children: [
                          Icon(Icons.lightbulb, color: Colors.amber),
                          SizedBox(width: 16),
                          Text(
                            'Tips & Tricks',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),

                      // Tips list
                      _buildTipItem(
                        'Enter readings at consistent times for more accurate averages',
                      ),
                      _buildTipItem(
                        'Use the history screen to identify usage patterns',
                      ),
                      _buildTipItem(
                        'Set up notifications to avoid running out of credit',
                      ),
                      _buildTipItem(
                        'Export your data regularly for safekeeping',
                      ),
                      _buildTipItem(
                        'Check the cost screen to monitor your spending',
                      ),

                      // Notification-specific tips
                      _buildNotificationTipSection(),
                    ],
                  ),
                ),
              ),

              // Contact section
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Row(
                        children: [
                          Icon(Icons.contact_support, color: Colors.blue),
                          SizedBox(width: 16),
                          Text(
                            'Contact & Support',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),

                      // Contact info
                      const Text(
                        'If you have any questions, suggestions, or issues, please contact us:',
                        style: TextStyle(
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Email button
                      LekkyButton(
                        text: 'Send Email',
                        type: LekkyButtonType.info,
                        size: LekkyButtonSize.standard,
                        onPressed: () {
                          // TODO: Implement email functionality
                        },
                      ),

                      const SizedBox(height: 8),

                      // Feedback button
                      LekkyButton(
                        text: 'Send Feedback',
                        type: LekkyButtonType.success,
                        size: LekkyButtonSize.standard,
                        onPressed: () {
                          // TODO: Implement feedback functionality
                        },
                      ),

                      const SizedBox(height: 16),

                      // Copyright
                      const Center(
                        child: Text(
                          '© 2023 Lekky App. All rights reserved.',
                          style: TextStyle(
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildFeatureItem(IconData icon, String title, String description) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, size: 20, color: Colors.blue),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                Text(
                  description,
                  style: const TextStyle(
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTipItem(String tip) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Icon(Icons.check_circle, size: 20, color: Colors.green),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              tip,
              style: const TextStyle(
                fontSize: 14,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build notification-specific tips section
  Widget _buildNotificationTipSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 16),

        // Notification section header
        const Row(
          children: [
            Icon(Icons.notifications_active, size: 18, color: Colors.orange),
            SizedBox(width: 8),
            Text(
              'Notification Tips',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.orange,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),

        // Notification tips
        _buildNotificationTip(
          Icons.notifications,
          'Tap the notification bell icon on the Dashboard to view all notifications',
        ),
        _buildNotificationTip(
          Icons.warning,
          'Set up Low Balance alerts to get notified when your meter balance drops below your threshold',
        ),
        _buildNotificationTip(
          Icons.schedule,
          'Configure "Days in Advance" to receive top-up reminders before you run out of credit',
        ),
        _buildNotificationTip(
          Icons.swipe,
          'Swipe left on notifications to mark as read, swipe right to delete',
        ),
        _buildNotificationTip(
          Icons.group,
          'Notifications are grouped by type: Low Balance, Top-up Reminders, Invalid Records, etc.',
        ),
        _buildNotificationTip(
          Icons.auto_delete,
          'Old notifications are automatically cleaned up after 30 days to keep the app running smoothly',
        ),
        _buildNotificationTip(
          Icons.settings,
          'Customize notification types and thresholds in Settings > Alerts & Notifications',
        ),
        _buildNotificationTip(
          Icons.phone_android,
          'Enable system notifications to receive alerts even when the app is closed',
        ),
      ],
    );
  }

  /// Build individual notification tip item
  Widget _buildNotificationTip(IconData icon, String tip) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 10.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, size: 16, color: Colors.blue.shade600),
          const SizedBox(width: 10),
          Expanded(
            child: Text(
              tip,
              style: const TextStyle(
                fontSize: 13,
                height: 1.3,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
