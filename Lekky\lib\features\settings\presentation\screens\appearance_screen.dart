import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../controllers/settings_controller.dart';
import '../../../../core/shared/models/theme_mode.dart';

/// Appearance settings screen
class AppearanceScreen extends StatelessWidget {
  /// Constructor
  const AppearanceScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Appearance'),
        backgroundColor: const Color(0xFF424242),
        foregroundColor: Colors.white,
      ),
      body: Consumer<SettingsController>(
        builder: (context, controller, _) {
          return ListView(
            padding: const EdgeInsets.all(16.0),
            children: [
              // Theme mode card
              Card(
                margin: const EdgeInsets.only(bottom: 16.0),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Row(
                        children: [
                          Icon(Icons.palette, color: Colors.blue),
                          SizedBox(width: 16),
                          Text(
                            'Theme Mode',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      
                      // System theme option
                      RadioListTile<AppThemeMode>(
                        title: const Text('System Default'),
                        subtitle: const Text('Follow system theme settings'),
                        value: AppThemeMode.system,
                        groupValue: controller.themeMode,
                        onChanged: (value) {
                          if (value != null) {
                            controller.updateThemeMode(value);
                          }
                        },
                        secondary: const Icon(Icons.brightness_auto),
                      ),
                      
                      // Light theme option
                      RadioListTile<AppThemeMode>(
                        title: const Text('Light Mode'),
                        subtitle: const Text('Always use light theme'),
                        value: AppThemeMode.light,
                        groupValue: controller.themeMode,
                        onChanged: (value) {
                          if (value != null) {
                            controller.updateThemeMode(value);
                          }
                        },
                        secondary: const Icon(Icons.light_mode),
                      ),
                      
                      // Dark theme option
                      RadioListTile<AppThemeMode>(
                        title: const Text('Dark Mode'),
                        subtitle: const Text('Always use dark theme'),
                        value: AppThemeMode.dark,
                        groupValue: controller.themeMode,
                        onChanged: (value) {
                          if (value != null) {
                            controller.updateThemeMode(value);
                          }
                        },
                        secondary: const Icon(Icons.dark_mode),
                      ),
                    ],
                  ),
                ),
              ),
              
              // Theme preview card
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Row(
                        children: [
                          Icon(Icons.preview, color: Colors.blue),
                          SizedBox(width: 16),
                          Text(
                            'Theme Preview',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      
                      // Preview container
                      Container(
                        padding: const EdgeInsets.all(16.0),
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.surface,
                          borderRadius: BorderRadius.circular(8.0),
                          border: Border.all(
                            color: Theme.of(context).colorScheme.outline,
                            width: 1.0,
                          ),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Sample Text',
                              style: Theme.of(context).textTheme.headlineMedium,
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'This is how text will appear in the app.',
                              style: Theme.of(context).textTheme.bodyLarge,
                            ),
                            const SizedBox(height: 16),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                              children: [
                                ElevatedButton(
                                  onPressed: () {},
                                  child: const Text('Primary Button'),
                                ),
                                OutlinedButton(
                                  onPressed: () {},
                                  child: const Text('Secondary Button'),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      
                      const SizedBox(height: 16),
                      
                      // Note about theme changes
                      const Text(
                        'Note: Theme changes apply immediately across the app.',
                        style: TextStyle(
                          fontSize: 14,
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}
