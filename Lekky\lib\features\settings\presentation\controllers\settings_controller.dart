import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../../core/constants/preference_keys.dart';
import '../../../../core/shared/models/theme_mode.dart';
import '../../../../core/services/data_backup_service.dart';
import '../../../../core/database/database_helper.dart';
import '../../../../core/di/service_locator.dart';
import '../../../../core/providers/localization_provider.dart';
import '../../../../core/theme/theme_manager.dart';
// Removed: notification_controller.dart - migrated to Riverpod
import '../../../notifications/data/notification_service.dart';
import '../../../notifications/domain/models/notification.dart';
import '../../domain/models/settings_category.dart';
import '../../domain/models/settings_sub_branch.dart';
import '../../data/settings_state_service.dart';
import '../../domain/models/settings_state.dart';

/// Controller for the settings screen
class SettingsController extends ChangeNotifier {
  bool _isLoading = true;
  String? _error;

  /// Data backup service
  late DataBackupService _dataBackupService;

  /// Settings state service for preservation
  late SettingsStateService _settingsStateService;

  /// Last backup date
  DateTime? _lastBackupDate;

  /// Available backups
  List<String> _availableBackups = [];

  /// Auto backup enabled
  bool _autoBackupEnabled = false;

  /// Auto backup frequency
  String _autoBackupFrequency = 'weekly';

  // Section toggle states
  bool _isRegionEnabled = false;
  bool _isNotificationsEnabled = false;
  bool _isDateSettingsEnabled = false;
  bool _isAppearanceEnabled = false;
  bool _isDataBackupEnabled = false;
  bool _isAboutEnabled = false;
  bool _isDonateEnabled = false;
  bool _isTestingEnabled = false;

  // Expansion states
  int? _expandedCategoryIndex;

  // Settings values
  String _language = 'English';
  String _currency = 'GBP';
  String _currencySymbol = '£';
  double _alertThreshold = 5.0;
  int _daysInAdvance = 5;
  bool _notificationsEnabled = true;
  bool _remindersEnabled = false;
  bool _lowBalanceAlertsEnabled = true;
  bool _timeToTopUpAlertsEnabled = true;
  bool _invalidRecordAlertsEnabled = true;
  String _reminderFrequency = 'weekly';
  DateTime? _reminderStartDateTime;
  String _dateFormat = 'DD-MM-YYYY';
  bool _showTimeWithDate = false;
  AppThemeMode _themeMode = AppThemeMode.system;

  // Random tips for the info banner
  final List<String> _tips = [
    'Monitor trends to predict future costs.',
    'Use history to spot unusual patterns.',
    'Enter readings at consistent times daily.',
    'Review past top-ups for better budgeting.',
    'Check usage after adding new appliances.',
    'Update thresholds when your plan changes.',
    'Export history for spreadsheet analysis.',
    'Review your usage history to find savings.',
    'Date column shows when readings/top-ups happened.',
    'Total-avg shows usage since your first reading.',
    'Compare monthly averages to spot patterns.',
    'Check the app daily for better control.',
    'Test scenarios to see potential savings.',
    'Tap the notification bell to view all alerts.',
    'Set up low balance alerts to avoid running out of credit.',
    'Configure notification thresholds in Settings.',
    'Swipe notifications to mark as read or delete.',
    'Notifications are grouped by type for easy viewing.',
    'Old notifications are cleaned up automatically after 30 days.',
  ];

  String _currentTip = '';

  /// Constructor
  SettingsController() {
    _dataBackupService = DataBackupService(DatabaseHelper());
    _settingsStateService = SettingsStateService();
    _loadSettings();
    _currentTip = _getRandomTip();
    _loadBackupInfo();
  }

  /// Whether the controller is loading
  bool get isLoading => _isLoading;

  /// Error message if any
  String? get error => _error;

  /// Whether the region section is enabled
  bool get isRegionEnabled => _isRegionEnabled;

  /// Whether the notifications section is enabled
  bool get isNotificationsEnabled => _isNotificationsEnabled;

  /// Whether the date settings section is enabled
  bool get isDateSettingsEnabled => _isDateSettingsEnabled;

  /// Whether the appearance section is enabled
  bool get isAppearanceEnabled => _isAppearanceEnabled;

  /// Whether the data backup section is enabled
  bool get isDataBackupEnabled => _isDataBackupEnabled;

  /// Whether the about section is enabled
  bool get isAboutEnabled => _isAboutEnabled;

  /// Whether the donate section is enabled
  bool get isDonateEnabled => _isDonateEnabled;

  /// Whether the testing section is enabled
  bool get isTestingEnabled => _isTestingEnabled;

  /// Current language
  String get language => _language;

  /// Current currency
  String get currency => _currency;

  /// Current currency symbol
  String get currencySymbol => _currencySymbol;

  /// Current alert threshold
  double get alertThreshold => _alertThreshold;

  /// Current days in advance
  int get daysInAdvance => _daysInAdvance;

  /// Whether notifications are enabled
  bool get notificationsEnabled => _notificationsEnabled;

  /// Whether reminders are enabled
  bool get remindersEnabled => _remindersEnabled;

  /// Whether low balance alerts are enabled
  bool get lowBalanceAlertsEnabled => _lowBalanceAlertsEnabled;

  /// Whether time to top up alerts are enabled
  bool get timeToTopUpAlertsEnabled => _timeToTopUpAlertsEnabled;

  /// Whether invalid record alerts are enabled
  bool get invalidRecordAlertsEnabled => _invalidRecordAlertsEnabled;

  /// Current reminder frequency
  String get reminderFrequency => _reminderFrequency;

  /// Current reminder start date and time
  DateTime? get reminderStartDateTime => _reminderStartDateTime;

  /// Current date format
  String get dateFormat => _dateFormat;

  /// Whether to show time with date
  bool get showTimeWithDate => _showTimeWithDate;

  /// Current theme mode
  AppThemeMode get themeMode => _themeMode;

  /// Current tip
  String get currentTip => _currentTip;

  /// Last backup date
  DateTime? get lastBackupDate => _lastBackupDate;

  /// Available backups
  List<String> get availableBackups => _availableBackups;

  /// Auto backup enabled
  bool get autoBackupEnabled => _autoBackupEnabled;

  /// Auto backup frequency
  String get autoBackupFrequency => _autoBackupFrequency;

  /// Get the index of the expanded category
  int? get expandedCategoryIndex => _expandedCategoryIndex;

  /// Reset all toggles to off state (for settings screen entry)
  void resetAllToggles() {
    _isRegionEnabled = false;
    _isNotificationsEnabled = false;
    _isDateSettingsEnabled = false;
    _isAppearanceEnabled = false;
    _isDataBackupEnabled = false;
    _isAboutEnabled = false;
    _isDonateEnabled = false;
    _isTestingEnabled = false;
    _expandedCategoryIndex = null;
    notifyListeners();
  }

  /// Save current state for preservation during navigation
  Future<void> saveCurrentState() async {
    final state = SettingsState.fromController(
      isRegionEnabled: _isRegionEnabled,
      isNotificationsEnabled: _isNotificationsEnabled,
      isDateSettingsEnabled: _isDateSettingsEnabled,
      isAppearanceEnabled: _isAppearanceEnabled,
      isDataBackupEnabled: _isDataBackupEnabled,
      isAboutEnabled: _isAboutEnabled,
      isDonateEnabled: _isDonateEnabled,
      isTestingEnabled: _isTestingEnabled,
      expandedCategoryIndex: _expandedCategoryIndex,
    );

    await _settingsStateService.saveSettingsState(state);
  }

  /// Restore state from preservation
  Future<void> restoreState() async {
    final state = await _settingsStateService.restoreSettingsState();
    if (state != null) {
      _isRegionEnabled = state.categoryToggles[0] ?? false;
      _isNotificationsEnabled = state.categoryToggles[1] ?? false;
      _isDateSettingsEnabled = state.categoryToggles[2] ?? false;
      _isAppearanceEnabled = state.categoryToggles[3] ?? false;
      _isDataBackupEnabled = state.categoryToggles[4] ?? false;
      _isAboutEnabled = state.categoryToggles[5] ?? false;
      _isDonateEnabled = state.categoryToggles[6] ?? false;
      _isTestingEnabled = state.categoryToggles[7] ?? false;
      _expandedCategoryIndex = state.expandedCategoryIndex;
      notifyListeners();
    }
  }

  /// Check if preserved state exists
  Future<bool> hasPreservedState() async {
    return await _settingsStateService.hasValidState();
  }

  /// Clear preserved state
  Future<void> clearPreservedState() async {
    await _settingsStateService.clearSettingsState();
  }

  /// Toggle region section
  void toggleRegion(bool value) {
    _isRegionEnabled = value;

    // When toggling on, expand the region category (index 0)
    // When toggling off, collapse the category
    if (value) {
      // Turn off the toggle for the previously expanded category
      if (_expandedCategoryIndex != null && _expandedCategoryIndex != 0) {
        _turnOffToggleForCategory(_expandedCategoryIndex!);
      }
      _expandedCategoryIndex = 0; // Region is the first category (index 0)
    } else {
      _expandedCategoryIndex = null; // Always collapse when toggled off
    }

    notifyListeners();
  }

  /// Toggle notifications section
  void toggleNotifications(bool value) {
    _isNotificationsEnabled = value;

    // When toggling on, expand the notifications category (index 1)
    // When toggling off, collapse the category
    if (value) {
      // Turn off the toggle for the previously expanded category
      if (_expandedCategoryIndex != null && _expandedCategoryIndex != 1) {
        _turnOffToggleForCategory(_expandedCategoryIndex!);
      }
      _expandedCategoryIndex =
          1; // Notifications is the second category (index 1)
    } else {
      _expandedCategoryIndex = null; // Always collapse when toggled off
    }

    notifyListeners();
  }

  /// Toggle date settings section
  void toggleDateSettings(bool value) {
    _isDateSettingsEnabled = value;

    // When toggling on, expand the date settings category (index 2)
    // When toggling off, collapse the category
    if (value) {
      // Turn off the toggle for the previously expanded category
      if (_expandedCategoryIndex != null && _expandedCategoryIndex != 2) {
        _turnOffToggleForCategory(_expandedCategoryIndex!);
      }
      _expandedCategoryIndex =
          2; // Date Settings is the third category (index 2)
    } else {
      _expandedCategoryIndex = null; // Always collapse when toggled off
    }

    notifyListeners();
  }

  /// Toggle appearance section
  void toggleAppearance(bool value) {
    _isAppearanceEnabled = value;

    // When toggling on, expand the appearance category (index 3)
    // When toggling off, collapse the category
    if (value) {
      // Turn off the toggle for the previously expanded category
      if (_expandedCategoryIndex != null && _expandedCategoryIndex != 3) {
        _turnOffToggleForCategory(_expandedCategoryIndex!);
      }
      _expandedCategoryIndex = 3; // Appearance is the fourth category (index 3)
    } else {
      _expandedCategoryIndex = null; // Always collapse when toggled off
    }

    notifyListeners();
  }

  /// Toggle data backup section
  void toggleDataBackup(bool value) {
    _isDataBackupEnabled = value;

    // When toggling on, expand the data backup category (index 4)
    // When toggling off, collapse the category
    if (value) {
      // Turn off the toggle for the previously expanded category
      if (_expandedCategoryIndex != null && _expandedCategoryIndex != 4) {
        _turnOffToggleForCategory(_expandedCategoryIndex!);
      }
      _expandedCategoryIndex = 4; // Data Backup is the fifth category (index 4)
    } else {
      _expandedCategoryIndex = null; // Always collapse when toggled off
    }

    notifyListeners();
  }

  /// Toggle about section
  void toggleAbout(bool value) {
    _isAboutEnabled = value;

    // When toggling on, expand the about category (index 5)
    // When toggling off, collapse the category
    if (value) {
      // Turn off the toggle for the previously expanded category
      if (_expandedCategoryIndex != null && _expandedCategoryIndex != 5) {
        _turnOffToggleForCategory(_expandedCategoryIndex!);
      }
      _expandedCategoryIndex = 5; // About is the sixth category (index 5)
    } else {
      _expandedCategoryIndex = null; // Always collapse when toggled off
    }

    notifyListeners();
  }

  /// Toggle donate section
  void toggleDonate(bool value) {
    _isDonateEnabled = value;

    // When toggling on, expand the donate category (index 6)
    // When toggling off, collapse the category
    if (value) {
      // Turn off the toggle for the previously expanded category
      if (_expandedCategoryIndex != null && _expandedCategoryIndex != 6) {
        _turnOffToggleForCategory(_expandedCategoryIndex!);
      }
      _expandedCategoryIndex = 6; // Donate is the seventh category (index 6)
    } else {
      _expandedCategoryIndex = null; // Always collapse when toggled off
    }

    notifyListeners();
  }

  /// Toggle testing section
  void toggleTesting(bool value) {
    _isTestingEnabled = value;

    // When toggling on, expand the testing category (index 7)
    // When toggling off, collapse the category
    if (value) {
      // Turn off the toggle for the previously expanded category
      if (_expandedCategoryIndex != null && _expandedCategoryIndex != 7) {
        _turnOffToggleForCategory(_expandedCategoryIndex!);
      }
      _expandedCategoryIndex = 7; // Testing is the eighth category (index 7)
    } else {
      _expandedCategoryIndex = null; // Always collapse when toggled off
    }

    notifyListeners();
  }

  /// Update language
  Future<void> updateLanguage(String language) async {
    if (_language == language) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(PreferenceKeys.language, language);

      _language = language;

      // Update the localization provider
      try {
        final localizationProvider = serviceLocator<LocalizationProvider>();
        await localizationProvider.setLocale(_getLanguageCode(language));
      } catch (e) {
        debugPrint('Error updating localization provider: $e');
      }

      notifyListeners();
    } catch (e) {
      _error = e.toString();
      notifyListeners();
    }
  }

  /// Convert language name to language code
  String _getLanguageCode(String language) {
    switch (language) {
      case 'English':
        return 'en';
      case 'Spanish':
        return 'es';
      case 'French':
        return 'fr';
      case 'German':
        return 'de';
      case 'Italian':
        return 'it';
      case 'Portuguese':
        return 'pt';
      case 'Russian':
        return 'ru';
      case 'Chinese':
        return 'zh';
      case 'Japanese':
        return 'ja';
      case 'Hindi':
        return 'hi';
      default:
        return 'en';
    }
  }

  /// Update currency
  Future<void> updateCurrency(String currency, String symbol) async {
    if (_currency == currency && _currencySymbol == symbol) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(PreferenceKeys.currency, currency);
      await prefs.setString(PreferenceKeys.currencySymbol, symbol);

      _currency = currency;
      _currencySymbol = symbol;
      notifyListeners();
    } catch (e) {
      _error = e.toString();
      notifyListeners();
    }
  }

  /// Update alert threshold
  Future<void> updateAlertThreshold(double threshold) async {
    if (_alertThreshold == threshold) return;

    try {
      // Ensure threshold is within valid range
      if (threshold >= 1.00 && threshold <= 999.99) {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setDouble(PreferenceKeys.alertThreshold, threshold);

        _alertThreshold = threshold;
        notifyListeners();
      }
    } catch (e) {
      _error = e.toString();
      notifyListeners();
    }
  }

  /// Update days in advance
  Future<void> updateDaysInAdvance(int days) async {
    if (_daysInAdvance == days) return;

    try {
      // Ensure days is within valid range
      if (days >= 1 && days <= 99) {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setInt(PreferenceKeys.daysInAdvance, days);

        _daysInAdvance = days;
        notifyListeners();
      }
    } catch (e) {
      _error = e.toString();
      notifyListeners();
    }
  }

  /// Update notifications enabled
  Future<void> updateNotificationsEnabled(bool enabled) async {
    if (_notificationsEnabled == enabled) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(PreferenceKeys.notificationsEnabled, enabled);

      _notificationsEnabled = enabled;

      // Update notification system
      try {
        if (enabled) {
          // Initialize the notification service if enabled
          final notificationService =
              await serviceLocator.getAsync<NotificationService>();
          await notificationService.initialize();
        }
      } catch (e) {
        debugPrint('Failed to update notification system: $e');
      }

      notifyListeners();
    } catch (e) {
      _error = e.toString();
      notifyListeners();
    }
  }

  /// Update reminders enabled
  Future<void> updateRemindersEnabled(bool enabled) async {
    if (_remindersEnabled == enabled) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(PreferenceKeys.remindersEnabled, enabled);

      _remindersEnabled = enabled;

      // Clear start date/time when reminders disabled
      if (!enabled) {
        await updateReminderStartDateTime(null);
      }

      // Schedule a test reminder if enabled
      if (enabled && _notificationsEnabled) {
        try {
          // NotificationController migrated to Riverpod - test reminders handled by providers
          debugPrint(
              'Test reading reminder scheduling handled by Riverpod providers');
        } catch (e) {
          debugPrint('Failed to schedule test reading reminder: $e');
        }
      }

      notifyListeners();
    } catch (e) {
      _error = e.toString();
      notifyListeners();
    }
  }

  /// Update low balance alerts enabled
  Future<void> updateLowBalanceAlertsEnabled(bool enabled) async {
    if (_lowBalanceAlertsEnabled == enabled) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('low_balance_alerts_enabled', enabled);

      _lowBalanceAlertsEnabled = enabled;

      // Create a test notification if enabled
      if (enabled && _notificationsEnabled) {
        try {
          // NotificationController migrated to Riverpod - test notifications handled by providers
          debugPrint(
              'Test low balance notification handled by Riverpod providers');
        } catch (e) {
          debugPrint('Failed to create test low balance notification: $e');
        }
      }

      notifyListeners();
    } catch (e) {
      _error = e.toString();
      notifyListeners();
    }
  }

  /// Update time to top up alerts enabled
  Future<void> updateTimeToTopUpAlertsEnabled(bool enabled) async {
    if (_timeToTopUpAlertsEnabled == enabled) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('time_to_top_up_alerts_enabled', enabled);

      _timeToTopUpAlertsEnabled = enabled;

      // Create a test notification if enabled
      if (enabled && _notificationsEnabled) {
        try {
          // NotificationController migrated to Riverpod - test notifications handled by providers
          debugPrint(
              'Test time to top-up notification handled by Riverpod providers');
        } catch (e) {
          debugPrint('Failed to create test time to top-up notification: $e');
        }
      }

      notifyListeners();
    } catch (e) {
      _error = e.toString();
      notifyListeners();
    }
  }

  /// Update invalid record alerts enabled
  Future<void> updateInvalidRecordAlertsEnabled(bool enabled) async {
    if (_invalidRecordAlertsEnabled == enabled) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('invalid_record_alerts_enabled', enabled);

      _invalidRecordAlertsEnabled = enabled;

      // Create a test notification if enabled
      if (enabled && _notificationsEnabled) {
        try {
          // NotificationController migrated to Riverpod - test notifications handled by providers
          debugPrint(
              'Test invalid record notification handled by Riverpod providers');
        } catch (e) {
          debugPrint('Failed to create test invalid record notification: $e');
        }
      }

      notifyListeners();
    } catch (e) {
      _error = e.toString();
      notifyListeners();
    }
  }

  /// Update reminder frequency
  Future<void> updateReminderFrequency(String frequency) async {
    if (_reminderFrequency == frequency) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('reminder_frequency', frequency);

      _reminderFrequency = frequency;

      // Set default start date/time if not set
      if (_reminderStartDateTime == null) {
        await _setDefaultReminderStartDateTime();
      }

      notifyListeners();
    } catch (e) {
      _error = e.toString();
      notifyListeners();
    }
  }

  /// Update reminder start date and time
  Future<void> updateReminderStartDateTime(DateTime? dateTime) async {
    if (_reminderStartDateTime == dateTime) return;

    try {
      final prefs = await SharedPreferences.getInstance();

      if (dateTime != null) {
        await prefs.setString(
            PreferenceKeys.reminderStartDateTime, dateTime.toIso8601String());
      } else {
        await prefs.remove(PreferenceKeys.reminderStartDateTime);
      }

      _reminderStartDateTime = dateTime;

      // Schedule reminder notification if enabled
      if (_remindersEnabled && _notificationsEnabled && dateTime != null) {
        await _scheduleReminderNotification(dateTime);
      }

      notifyListeners();
    } catch (e) {
      _error = e.toString();
      notifyListeners();
    }
  }

  /// Set default reminder start date/time based on frequency
  Future<void> _setDefaultReminderStartDateTime() async {
    final now = DateTime.now();
    DateTime defaultDateTime;

    if (_reminderFrequency == 'daily') {
      // Daily: today at 19:00
      defaultDateTime = DateTime(now.year, now.month, now.day, 19, 0);
    } else {
      // Others: today at 19:00 (will be adjusted by user if needed)
      defaultDateTime = DateTime(now.year, now.month, now.day, 19, 0);
    }

    await updateReminderStartDateTime(defaultDateTime);
  }

  /// Schedule reminder notification
  Future<void> _scheduleReminderNotification(DateTime startDateTime) async {
    try {
      final notificationService =
          await serviceLocator.getAsync<NotificationService>();

      // Calculate next reminder date based on frequency
      final nextReminderDate = _calculateNextReminderDate(startDateTime);

      if (nextReminderDate != null) {
        // Create notification for meter reading reminder
        final notification = AppNotification(
          title: 'Meter Reading Reminder',
          message: 'It\'s time to take a new meter reading.',
          timestamp: nextReminderDate,
          type: NotificationType.readingReminder,
        );

        await notificationService.scheduleNotification(
            notification, nextReminderDate);
        debugPrint(
            'Reminder notification scheduled for ${nextReminderDate.toIso8601String()}');
      }
    } catch (e) {
      debugPrint('Failed to schedule reminder notification: $e');
    }
  }

  /// Calculate next reminder date based on frequency and start date
  DateTime? _calculateNextReminderDate(DateTime startDateTime) {
    final now = DateTime.now();

    switch (_reminderFrequency) {
      case 'daily':
        // For daily, if start time has passed today, schedule for tomorrow
        var nextDate = DateTime(now.year, now.month, now.day,
            startDateTime.hour, startDateTime.minute);
        if (nextDate.isBefore(now)) {
          nextDate = nextDate.add(const Duration(days: 1));
        }
        return nextDate;

      case 'weekly':
        // For weekly, find next occurrence of the same day of week
        var nextDate = startDateTime;
        while (nextDate.isBefore(now)) {
          nextDate = nextDate.add(const Duration(days: 7));
        }
        return nextDate;

      case 'bi-weekly':
        // For bi-weekly, add 14 days until future date
        var nextDate = startDateTime;
        while (nextDate.isBefore(now)) {
          nextDate = nextDate.add(const Duration(days: 14));
        }
        return nextDate;

      case 'monthly':
        // For monthly, find next occurrence of the same day of month
        var nextDate = DateTime(now.year, now.month, startDateTime.day,
            startDateTime.hour, startDateTime.minute);
        if (nextDate.isBefore(now)) {
          // Try next month
          if (now.month == 12) {
            nextDate = DateTime(now.year + 1, 1, startDateTime.day,
                startDateTime.hour, startDateTime.minute);
          } else {
            nextDate = DateTime(now.year, now.month + 1, startDateTime.day,
                startDateTime.hour, startDateTime.minute);
          }
        }
        return nextDate;

      default:
        return null;
    }
  }

  /// Update date format
  Future<void> updateDateFormat(String format) async {
    if (_dateFormat == format) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(PreferenceKeys.dateFormat, format);

      _dateFormat = format;
      notifyListeners();
    } catch (e) {
      _error = e.toString();
      notifyListeners();
    }
  }

  /// Update show time with date
  Future<void> updateShowTimeWithDate(bool show) async {
    if (_showTimeWithDate == show) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(PreferenceKeys.showTimeWithDate, show);

      _showTimeWithDate = show;
      notifyListeners();
    } catch (e) {
      _error = e.toString();
      notifyListeners();
    }
  }

  /// Update theme mode
  Future<void> updateThemeMode(AppThemeMode mode) async {
    if (_themeMode == mode) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(PreferenceKeys.themeMode, mode.toString());

      _themeMode = mode;

      // Apply theme change immediately
      final themeManager = serviceLocator<ThemeManager>();
      await themeManager.setThemeMode(mode);

      notifyListeners();
    } catch (e) {
      _error = e.toString();
      notifyListeners();
    }
  }

  /// Get a random tip
  String _getRandomTip() {
    return _tips[DateTime.now().millisecond % _tips.length];
  }

  /// Refresh the current tip
  void refreshTip() {
    _currentTip = _getRandomTip();
    notifyListeners();
  }

  /// Load settings from shared preferences
  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Toggle states are now temporary UI state only - not loaded from preferences

      // Load settings values
      _language = prefs.getString(PreferenceKeys.language) ?? 'English';
      _currency = prefs.getString(PreferenceKeys.currency) ?? 'GBP';
      _currencySymbol = prefs.getString(PreferenceKeys.currencySymbol) ?? '£';
      _alertThreshold = prefs.getDouble(PreferenceKeys.alertThreshold) ?? 5.0;
      _daysInAdvance = prefs.getInt(PreferenceKeys.daysInAdvance) ?? 5;
      _notificationsEnabled =
          prefs.getBool(PreferenceKeys.notificationsEnabled) ?? true;
      _remindersEnabled =
          prefs.getBool(PreferenceKeys.remindersEnabled) ?? false;
      _lowBalanceAlertsEnabled =
          prefs.getBool('low_balance_alerts_enabled') ?? true;
      _timeToTopUpAlertsEnabled =
          prefs.getBool('time_to_top_up_alerts_enabled') ?? true;
      _invalidRecordAlertsEnabled =
          prefs.getBool('invalid_record_alerts_enabled') ?? true;

      // Migration: Convert old "timeToNoUnits" preference to "lowBalance"
      final hasOldTimeToNoUnits =
          prefs.getBool('time_to_no_units_alerts_enabled');
      if (hasOldTimeToNoUnits == true) {
        await prefs.setBool('low_balance_alerts_enabled', true);
        await prefs.remove('time_to_no_units_alerts_enabled');
        _lowBalanceAlertsEnabled = true;
      }
      _reminderFrequency = prefs.getString('reminder_frequency') ?? 'weekly';

      // Load reminder start date/time
      final reminderStartDateTimeString =
          prefs.getString(PreferenceKeys.reminderStartDateTime);
      if (reminderStartDateTimeString != null) {
        try {
          _reminderStartDateTime = DateTime.parse(reminderStartDateTimeString);
        } catch (e) {
          _reminderStartDateTime = null;
        }
      }

      _dateFormat = prefs.getString(PreferenceKeys.dateFormat) ?? 'DD-MM-YYYY';
      _showTimeWithDate =
          prefs.getBool(PreferenceKeys.showTimeWithDate) ?? false;

      final themeModeString = prefs.getString(PreferenceKeys.themeMode);
      if (themeModeString != null) {
        _themeMode = AppThemeMode.fromString(themeModeString);
      }

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Load backup information
  Future<void> _loadBackupInfo() async {
    try {
      _lastBackupDate = await _dataBackupService.getLastBackupDate();
      _availableBackups = await _dataBackupService.getAvailableBackups();

      final autoBackupSettings =
          await _dataBackupService.getAutoBackupSettings();
      _autoBackupEnabled = autoBackupSettings['enabled'] as bool;
      _autoBackupFrequency = autoBackupSettings['frequency'] as String;

      notifyListeners();
    } catch (e) {
      _error = e.toString();
      notifyListeners();
    }
  }

  /// Create a backup
  Future<String?> createBackup() async {
    try {
      _isLoading = true;
      notifyListeners();

      final backupPath = await _dataBackupService.createBackup();

      if (backupPath != null) {
        _lastBackupDate = DateTime.now();
        _availableBackups = await _dataBackupService.getAvailableBackups();
      }

      _isLoading = false;
      notifyListeners();

      return backupPath;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return null;
    }
  }

  /// Restore from a backup
  Future<bool> restoreFromBackup(String backupPath) async {
    try {
      _isLoading = true;
      notifyListeners();

      final result = await _dataBackupService.restoreFromBackup(backupPath);

      _isLoading = false;
      notifyListeners();

      return result;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }

  /// Export data to CSV
  Future<String?> exportToCsv() async {
    try {
      _isLoading = true;
      notifyListeners();

      final csvPath = await _dataBackupService.exportToCsv();

      _isLoading = false;
      notifyListeners();

      return csvPath;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return null;
    }
  }

  /// Update auto backup settings
  Future<bool> updateAutoBackupSettings({
    required bool enabled,
    required String frequency,
  }) async {
    try {
      _isLoading = true;
      notifyListeners();

      final result = await _dataBackupService.setAutoBackupSettings(
        enabled: enabled,
        frequency: frequency,
      );

      if (result) {
        _autoBackupEnabled = enabled;
        _autoBackupFrequency = frequency;
      }

      _isLoading = false;
      notifyListeners();

      return result;
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }

  /// Refresh backup information
  Future<void> refreshBackupInfo() async {
    await _loadBackupInfo();
  }

  /// Clear any error message
  void clearError() {
    _error = null;
    notifyListeners();
  }

  /// Expand or collapse a category
  void expandCategory(int index) {
    if (_expandedCategoryIndex == index) {
      // Collapse if already expanded
      _expandedCategoryIndex = null;

      // Turn off the toggle for the collapsed category
      _turnOffToggleForCategory(index);
    } else {
      // Turn off the toggle for the previously expanded category
      if (_expandedCategoryIndex != null) {
        _turnOffToggleForCategory(_expandedCategoryIndex!);
      }

      // Expand new category
      _expandedCategoryIndex = index;
    }
    notifyListeners();
  }

  /// Turn off the toggle for a specific category
  void _turnOffToggleForCategory(int index) {
    switch (index) {
      case 0: // Region
        if (_isRegionEnabled) {
          _isRegionEnabled = false;
        }
        break;
      case 1: // Notifications
        if (_isNotificationsEnabled) {
          _isNotificationsEnabled = false;
        }
        break;
      case 2: // Date Settings
        if (_isDateSettingsEnabled) {
          _isDateSettingsEnabled = false;
        }
        break;
      case 3: // Appearance
        if (_isAppearanceEnabled) {
          _isAppearanceEnabled = false;
        }
        break;
      case 4: // Data Backup
        if (_isDataBackupEnabled) {
          _isDataBackupEnabled = false;
        }
        break;
      case 5: // About
        if (_isAboutEnabled) {
          _isAboutEnabled = false;
        }
        break;
      case 6: // Donate
        if (_isDonateEnabled) {
          _isDonateEnabled = false;
        }
        break;
      case 7: // Testing
        if (_isTestingEnabled) {
          _isTestingEnabled = false;
        }
        break;
    }
  }
}
