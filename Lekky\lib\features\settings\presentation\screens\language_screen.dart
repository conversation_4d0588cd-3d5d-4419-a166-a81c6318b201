import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../controllers/settings_controller.dart';
import '../../../../features/setup/presentation/widgets/radio_option.dart';

/// Language settings screen
class LanguageScreen extends StatelessWidget {
  /// Constructor
  const LanguageScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Language'),
        backgroundColor: const Color(0xFF424242),
        foregroundColor: Colors.white,
      ),
      body: Consumer<SettingsController>(
        builder: (context, controller, _) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Card(
                  margin: const EdgeInsets.all(8.0),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12.0),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Language section header
                        Row(
                          children: [
                            const Icon(Icons.language, color: Colors.blue),
                            const SizedBox(width: 16),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const Text(
                                    'Language',
                                    style: TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    'Current: ${controller.language}',
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: Theme.of(context).textTheme.bodySmall?.color,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        const Text(
                          'Select your preferred language for the app interface.',
                          style: TextStyle(fontSize: 14),
                        ),
                        const SizedBox(height: 8),
                        
                        // Language options
                        Column(
                          children: [
                            RadioOption<String>(
                              value: 'English',
                              groupValue: controller.language,
                              onChanged: controller.updateLanguage,
                              title: 'English',
                              icon: Icons.language,
                            ),
                            RadioOption<String>(
                              value: 'Spanish',
                              groupValue: controller.language,
                              onChanged: controller.updateLanguage,
                              title: 'Spanish',
                              icon: Icons.language,
                            ),
                            RadioOption<String>(
                              value: 'French',
                              groupValue: controller.language,
                              onChanged: controller.updateLanguage,
                              title: 'French',
                              icon: Icons.language,
                            ),
                            RadioOption<String>(
                              value: 'German',
                              groupValue: controller.language,
                              onChanged: controller.updateLanguage,
                              title: 'German',
                              icon: Icons.language,
                            ),
                            RadioOption<String>(
                              value: 'Italian',
                              groupValue: controller.language,
                              onChanged: controller.updateLanguage,
                              title: 'Italian',
                              icon: Icons.language,
                            ),
                            RadioOption<String>(
                              value: 'Portuguese',
                              groupValue: controller.language,
                              onChanged: controller.updateLanguage,
                              title: 'Portuguese',
                              icon: Icons.language,
                            ),
                            RadioOption<String>(
                              value: 'Russian',
                              groupValue: controller.language,
                              onChanged: controller.updateLanguage,
                              title: 'Russian',
                              icon: Icons.language,
                            ),
                            RadioOption<String>(
                              value: 'Chinese',
                              groupValue: controller.language,
                              onChanged: controller.updateLanguage,
                              title: 'Chinese',
                              icon: Icons.language,
                            ),
                            RadioOption<String>(
                              value: 'Japanese',
                              groupValue: controller.language,
                              onChanged: controller.updateLanguage,
                              title: 'Japanese',
                              icon: Icons.language,
                            ),
                            RadioOption<String>(
                              value: 'Hindi',
                              groupValue: controller.language,
                              onChanged: controller.updateLanguage,
                              title: 'Hindi',
                              icon: Icons.language,
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
