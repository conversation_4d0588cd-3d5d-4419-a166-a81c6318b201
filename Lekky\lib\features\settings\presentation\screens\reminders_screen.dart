import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../controllers/settings_controller.dart';
import '../../../../core/shared/widgets/settings_section_header.dart';

/// Reminders settings screen
class RemindersScreen extends StatelessWidget {
  /// Constructor
  const RemindersScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Reminders'),
        backgroundColor: const Color(0xFF424242),
        foregroundColor: Colors.white,
      ),
      body: Consumer<SettingsController>(
        builder: (context, controller, _) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Card(
                  margin: const EdgeInsets.all(8.0),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12.0),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SettingsSectionHeader(
                          title: 'Reminders',
                          description:
                              'Configure when you want to receive reminders.',
                          icon: Icons.alarm,
                        ),
                        const SizedBox(height: 16),

                        // Enable Reminders
                        SwitchListTile(
                          title: const Text('Enable Reminders'),
                          subtitle: const Text(
                              'Get regular reminders to check your meter.'),
                          value: controller.remindersEnabled,
                          onChanged: (value) {
                            controller.updateRemindersEnabled(value);
                          },
                        ),

                        const Divider(),

                        // Reminder Frequency
                        ListTile(
                          title: const Text('Reminder Frequency'),
                          subtitle: Text(controller.reminderFrequency),
                          trailing:
                              const Icon(Icons.arrow_forward_ios, size: 16),
                          onTap: () {
                            _showReminderFrequencyDialog(context, controller);
                          },
                        ),

                        // Reminder Schedule (only show if reminders enabled)
                        if (controller.remindersEnabled) ...[
                          const Divider(),
                          _buildReminderScheduleButton(context, controller),
                        ],
                      ],
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  void _showReminderFrequencyDialog(
      BuildContext context, SettingsController controller) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Reminder Frequency'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              RadioListTile<String>(
                title: const Text('Daily'),
                value: 'daily',
                groupValue: controller.reminderFrequency,
                onChanged: (value) {
                  Navigator.pop(context);
                  if (value != null) controller.updateReminderFrequency(value);
                },
              ),
              RadioListTile<String>(
                title: const Text('Weekly'),
                value: 'weekly',
                groupValue: controller.reminderFrequency,
                onChanged: (value) {
                  Navigator.pop(context);
                  if (value != null) controller.updateReminderFrequency(value);
                },
              ),
              RadioListTile<String>(
                title: const Text('Bi-weekly'),
                value: 'bi-weekly',
                groupValue: controller.reminderFrequency,
                onChanged: (value) {
                  Navigator.pop(context);
                  if (value != null) controller.updateReminderFrequency(value);
                },
              ),
              RadioListTile<String>(
                title: const Text('Monthly'),
                value: 'monthly',
                groupValue: controller.reminderFrequency,
                onChanged: (value) {
                  Navigator.pop(context);
                  if (value != null) controller.updateReminderFrequency(value);
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
          ],
        );
      },
    );
  }

  /// Build reminder schedule button
  Widget _buildReminderScheduleButton(
      BuildContext context, SettingsController controller) {
    final startDateTime = controller.reminderStartDateTime;
    final frequency = controller.reminderFrequency;

    String buttonText;
    if (startDateTime == null) {
      buttonText = 'Set Reminder Time';
    } else if (frequency == 'daily') {
      buttonText =
          'Daily at ${TimeOfDay.fromDateTime(startDateTime).format(context)}';
    } else {
      final dateFormat = DateFormat('MMM d, y');
      final timeFormat = TimeOfDay.fromDateTime(startDateTime);
      buttonText =
          '${frequency.substring(0, 1).toUpperCase()}${frequency.substring(1)} starting ${dateFormat.format(startDateTime)} at ${timeFormat.format(context)}';
    }

    return ListTile(
      title: const Text('Reminder Schedule'),
      subtitle: Text(buttonText),
      trailing: const Icon(Icons.schedule),
      onTap: () => _showReminderDateTimePicker(context, controller),
    );
  }

  /// Show reminder date/time picker based on frequency
  Future<void> _showReminderDateTimePicker(
      BuildContext context, SettingsController controller) async {
    final frequency = controller.reminderFrequency;
    final currentDateTime = controller.reminderStartDateTime ?? DateTime.now();

    if (frequency == 'daily') {
      await _showTimeOnlyPicker(context, controller, currentDateTime);
    } else {
      await _showDateTimePicker(context, controller, currentDateTime);
    }
  }

  /// Show time-only picker for daily reminders
  Future<void> _showTimeOnlyPicker(BuildContext context,
      SettingsController controller, DateTime current) async {
    final TimeOfDay? pickedTime = await showTimePicker(
      context: context,
      initialTime: TimeOfDay.fromDateTime(current),
    );

    if (pickedTime != null) {
      final now = DateTime.now();
      var newDateTime = DateTime(
        now.year,
        now.month,
        now.day,
        pickedTime.hour,
        pickedTime.minute,
      );

      // If the time has already passed today, schedule for tomorrow
      if (newDateTime.isBefore(now)) {
        newDateTime = newDateTime.add(const Duration(days: 1));
      }

      await controller.updateReminderStartDateTime(newDateTime);
    }
  }

  /// Show date+time picker for non-daily reminders
  Future<void> _showDateTimePicker(BuildContext context,
      SettingsController controller, DateTime current) async {
    final now = DateTime.now();

    // First pick date
    final DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: current.isAfter(now) ? current : now,
      firstDate: now,
      lastDate: now.add(const Duration(days: 365)),
    );

    if (pickedDate != null && context.mounted) {
      // Then pick time
      final TimeOfDay? pickedTime = await showTimePicker(
        context: context,
        initialTime: TimeOfDay.fromDateTime(current),
      );

      if (pickedTime != null) {
        var newDateTime = DateTime(
          pickedDate.year,
          pickedDate.month,
          pickedDate.day,
          pickedTime.hour,
          pickedTime.minute,
        );

        // Ensure the datetime is in the future
        if (newDateTime.isBefore(now)) {
          newDateTime = DateTime(
            now.year,
            now.month,
            now.day,
            pickedTime.hour,
            pickedTime.minute,
          ).add(const Duration(days: 1));
        }

        await controller.updateReminderStartDateTime(newDateTime);
      }
    }
  }
}
