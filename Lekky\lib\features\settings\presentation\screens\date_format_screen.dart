import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../controllers/settings_controller.dart';
import '../../../../core/shared/models/date_format.dart';
import '../../../../core/shared/widgets/settings_section_header.dart';
import '../../../../core/shared/widgets/settings_radio.dart';

/// Date Format settings screen
class DateFormatScreen extends StatelessWidget {
  /// Constructor
  const DateFormatScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Date Format'),
        backgroundColor: const Color(0xFF424242),
        foregroundColor: Colors.white,
      ),
      body: Consumer<SettingsController>(
        builder: (context, controller, _) {
          // Convert string date format to enum
          DateFormat dateFormat = DateFormat.values.firstWhere(
            (format) =>
                format.toString().split('.').last == controller.dateFormat,
            orElse: () => DateFormat.ddMmYyyy,
          );

          return SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Date format card
                Card(
                  margin: const EdgeInsets.all(8.0),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12.0),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SettingsSectionHeader(
                          title: 'Date Format',
                          description:
                              'Choose how dates will be displayed throughout the app.',
                          icon: Icons.calendar_today,
                        ),
                        const SizedBox(height: 16),
                        SettingsRadio<DateFormat>(
                          title: 'Date Format',
                          value: dateFormat,
                          onChanged: (format) {
                            // Convert enum back to string
                            final formatString =
                                format.toString().split('.').last;
                            controller.updateDateFormat(formatString);
                          },
                          options: [
                            SettingsRadioOption<DateFormat>(
                              value: DateFormat.ddMmYyyy,
                              title: 'DD-MM-YYYY',
                              description: 'Example: 11-05-2025',
                            ),
                            SettingsRadioOption<DateFormat>(
                              value: DateFormat.mmDdYyyy,
                              title: 'MM-DD-YYYY',
                              description: 'Example: 05-11-2025',
                            ),
                            SettingsRadioOption<DateFormat>(
                              value: DateFormat.yyyyMmDd,
                              title: 'YYYY-MM-DD',
                              description: 'Example: 2025-05-11',
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
