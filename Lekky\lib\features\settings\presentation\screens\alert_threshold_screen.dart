import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../controllers/settings_controller.dart';
import '../../../../core/shared/widgets/settings_section_header.dart';
import '../../../../core/shared/widgets/currency_input_field.dart';

/// Alert Threshold settings screen
class AlertThresholdScreen extends StatelessWidget {
  /// Constructor
  const AlertThresholdScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Alert Threshold'),
        backgroundColor: const Color(0xFF424242),
        foregroundColor: Colors.white,
      ),
      body: Consumer<SettingsController>(
        builder: (context, controller, _) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Alert settings card
                Card(
                  margin: const EdgeInsets.all(8.0),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12.0),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SettingsSectionHeader(
                          title: 'Alert Settings',
                          description:
                              'Configure when you want to receive alerts.',
                          icon: Icons.notifications,
                        ),

                        // Alert Threshold Subsection
                        const Text(
                          'Alert Threshold',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        const Text(
                          'Get notified when your balance falls below this amount.',
                          style: TextStyle(fontSize: 14),
                        ),
                        const SizedBox(height: 8),

                        CurrencyInputField(
                          value: controller.alertThreshold,
                          onChanged: (value) => value != null
                              ? controller.updateAlertThreshold(value)
                              : null,
                          currencySymbol: controller.currencySymbol,
                          labelText: 'Alert Threshold',
                          hintText: 'Enter amount',
                          minValue: 1.00,
                          maxValue: 999.99,
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
