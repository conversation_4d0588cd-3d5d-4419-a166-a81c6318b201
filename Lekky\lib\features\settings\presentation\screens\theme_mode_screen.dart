import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../controllers/settings_controller.dart';
import '../../../../features/setup/presentation/widgets/appearance_settings_card.dart';
import '../../../../core/shared/models/theme_mode.dart';

/// Theme Mode settings screen
class ThemeModeScreen extends StatelessWidget {
  /// Constructor
  const ThemeModeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Theme Mode'),
        backgroundColor: const Color(0xFF424242),
        foregroundColor: Colors.white,
      ),
      body: Consumer<SettingsController>(
        builder: (context, controller, _) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Appearance settings card
                AppearanceSettingsCard(
                  themeMode: controller.themeMode,
                  onThemeModeChanged: (mode) {
                    controller.updateThemeMode(mode);
                  },
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
