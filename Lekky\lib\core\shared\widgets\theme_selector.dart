import 'package:flutter/material.dart';
import '../models/theme_mode.dart';

/// A widget for selecting theme mode
class ThemeSelector extends StatelessWidget {
  /// Current theme mode
  final AppThemeMode currentThemeMode;
  
  /// Callback when theme mode changes
  final Function(AppThemeMode) onThemeModeChanged;
  
  /// Constructor
  const ThemeSelector({
    Key? key,
    required this.currentThemeMode,
    required this.onThemeModeChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Theme',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        const SizedBox(height: 8),
        _buildThemeOption(
          context,
          AppThemeMode.system,
          Icons.brightness_auto,
          'System Default',
          'Follow system theme settings',
        ),
        const SizedBox(height: 8),
        _buildThemeOption(
          context,
          AppThemeMode.light,
          Icons.brightness_high,
          'Light Mode',
          'Always use light theme',
        ),
        const SizedBox(height: 8),
        _buildThemeOption(
          context,
          AppThemeMode.dark,
          Icons.brightness_4,
          'Dark Mode',
          'Always use dark theme',
        ),
      ],
    );
  }

  Widget _buildThemeOption(
    BuildContext context,
    AppThemeMode themeMode,
    IconData icon,
    String title,
    String description,
  ) {
    return InkWell(
      onTap: () => onThemeModeChanged(themeMode),
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          border: Border.all(
            color: currentThemeMode == themeMode
                ? Theme.of(context).colorScheme.primary
                : Theme.of(context).dividerColor,
            width: currentThemeMode == themeMode ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Radio<AppThemeMode>(
              value: themeMode,
              groupValue: currentThemeMode,
              onChanged: (value) {
                if (value != null) {
                  onThemeModeChanged(value);
                }
              },
            ),
            const SizedBox(width: 8),
            Icon(icon),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    description,
                    style: TextStyle(
                      fontSize: 12,
                      color: Theme.of(context).textTheme.bodySmall?.color,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
