import 'package:sqflite/sqflite.dart';
import '../../../../core/constants/database_constants.dart';
import '../../../../core/database/database_helper.dart';
import '../../../../core/utils/logger.dart';
import '../../../../core/utils/bulk_operation_context.dart';
import '../../../../core/di/service_locator.dart';
import '../../../../core/shared/enums/entry_enums.dart';
import '../../../averages/domain/services/average_service.dart';
import '../../../averages/domain/repositories/per_reading_average_repository.dart';
import '../../../validation/domain/services/validation_trigger_service.dart';
import '../../../validation/domain/services/meter_reading_validator.dart';
import '../../domain/models/meter_reading.dart';
import '../../domain/repositories/meter_reading_repository.dart';

/// Implementation of the meter reading repository
class MeterReadingRepositoryImpl implements MeterReadingRepository {
  final DatabaseHelper _databaseHelper;

  /// Constructor
  MeterReadingRepositoryImpl(this._databaseHelper);

  @override
  Future<List<MeterReading>> getAllMeterReadings() async {
    try {
      final db = await _databaseHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        DatabaseConstants.meterReadingsTable,
        orderBy: 'date DESC',
      );

      return List.generate(maps.length, (i) {
        return MeterReading.fromMap(maps[i]);
      });
    } catch (e) {
      Logger.error('Failed to get all meter readings: $e');
      return [];
    }
  }

  @override
  Future<List<MeterReading>> getMeterReadings({
    required int page,
    required int pageSize,
  }) async {
    try {
      final db = await _databaseHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        DatabaseConstants.meterReadingsTable,
        orderBy: 'date DESC',
        limit: pageSize,
        offset: page * pageSize,
      );

      return List.generate(maps.length, (i) {
        return MeterReading.fromMap(maps[i]);
      });
    } catch (e) {
      Logger.error('Failed to get meter readings with pagination: $e');
      return [];
    }
  }

  @override
  Future<MeterReading?> getMeterReadingById(int id) async {
    try {
      final db = await _databaseHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        DatabaseConstants.meterReadingsTable,
        where: 'id = ?',
        whereArgs: [id],
      );

      if (maps.isNotEmpty) {
        return MeterReading.fromMap(maps.first);
      }
      return null;
    } catch (e) {
      Logger.error('Failed to get meter reading by ID: $e');
      return null;
    }
  }

  @override
  Future<List<MeterReading>> getMeterReadingsByDateRange({
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    try {
      final db = await _databaseHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        DatabaseConstants.meterReadingsTable,
        where: 'date BETWEEN ? AND ?',
        whereArgs: [
          startDate.toIso8601String(),
          endDate.toIso8601String(),
        ],
        orderBy: 'date DESC',
      );

      return List.generate(maps.length, (i) {
        return MeterReading.fromMap(maps[i]);
      });
    } catch (e) {
      Logger.error('Failed to get meter readings by date range: $e');
      return [];
    }
  }

  @override
  Future<MeterReading?> getLatestMeterReading() async {
    try {
      final db = await _databaseHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        DatabaseConstants.meterReadingsTable,
        orderBy: 'date DESC',
        limit: 1,
      );

      if (maps.isNotEmpty) {
        return MeterReading.fromMap(maps.first);
      }
      return null;
    } catch (e) {
      Logger.error('Failed to get latest meter reading: $e');
      return null;
    }
  }

  @override
  Future<int> addMeterReading(MeterReading meterReading) async {
    try {
      final db = await _databaseHelper.database;

      // Validate the meter reading before adding
      final validator = serviceLocator<MeterReadingValidator>();
      final isValid = await validator.validateMeterReading(meterReading);

      // Create a copy with the validation result
      final validatedReading = meterReading.copyWith(
        status: isValid ? EntryStatus.valid : EntryStatus.invalid,
      );

      final result = await db.insert(
        DatabaseConstants.meterReadingsTable,
        validatedReading.toMap(),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );

      // Update averages and trigger validation after successful insert
      if (result > 0) {
        BulkOperationContext.incrementOperationCount();

        // Only update averages and trigger validation if not in bulk operation
        if (!BulkOperationContext.isBulkOperation) {
          _updateAveragesAsync();
          _triggerValidationAsync(result);
          Logger.info(
              'MeterReadingRepository: Added meter reading and triggered average update and validation');
        } else {
          Logger.info(
              'MeterReadingRepository: Added meter reading (bulk operation)');
        }
      }

      return result;
    } catch (e) {
      Logger.error('Failed to add meter reading: $e');
      return -1;
    }
  }

  @override
  Future<int> updateMeterReading(MeterReading meterReading) async {
    try {
      final db = await _databaseHelper.database;

      // Validate the meter reading before updating
      final validator = serviceLocator<MeterReadingValidator>();
      final isValid = await validator.validateMeterReading(meterReading);

      // Create a copy with the validation result and updated timestamp
      final validatedReading = meterReading.copyWith(
        status: isValid ? EntryStatus.valid : EntryStatus.invalid,
        updatedAt: DateTime.now(),
      );

      final result = await db.update(
        DatabaseConstants.meterReadingsTable,
        validatedReading.toMap(),
        where: 'id = ?',
        whereArgs: [meterReading.id],
      );

      // Update averages and trigger validation after successful update
      if (result > 0) {
        BulkOperationContext.incrementOperationCount();

        // Only update averages and trigger validation if not in bulk operation
        if (!BulkOperationContext.isBulkOperation) {
          _updateAveragesAsync();
          _triggerValidationUpdateAsync(meterReading.id!);
          Logger.info(
              'MeterReadingRepository: Updated meter reading and triggered average update and validation');
        } else {
          Logger.info(
              'MeterReadingRepository: Updated meter reading (bulk operation)');
        }
      }

      return result;
    } catch (e) {
      Logger.error('Failed to update meter reading: $e');
      return -1;
    }
  }

  @override
  Future<int> deleteMeterReading(int id) async {
    try {
      // Get the meter reading date before deletion for validation
      final meterReading = await getMeterReadingById(id);
      final deletedDate = meterReading?.date;

      final db = await _databaseHelper.database;
      final result = await db.delete(
        DatabaseConstants.meterReadingsTable,
        where: 'id = ?',
        whereArgs: [id],
      );

      // Clean up orphaned per-reading average and update averages after successful delete
      if (result > 0) {
        // Clean up corresponding per-reading average
        await _cleanupPerReadingAverage(id);

        BulkOperationContext.incrementOperationCount();

        // Only update averages and trigger validation if not in bulk operation
        if (!BulkOperationContext.isBulkOperation) {
          _updateAveragesAsync();
          if (deletedDate != null) {
            _triggerValidationDeleteAsync(deletedDate);
          }
          Logger.info(
              'MeterReadingRepository: Deleted meter reading, cleaned up per-reading average, and triggered average update and validation');
        } else {
          Logger.info(
              'MeterReadingRepository: Deleted meter reading and cleaned up per-reading average (bulk operation)');
        }
      }

      return result;
    } catch (e) {
      Logger.error('Failed to delete meter reading: $e');
      return -1;
    }
  }

  @override
  Future<int> getMeterReadingsCount() async {
    try {
      final db = await _databaseHelper.database;
      final result = await db.rawQuery(
        'SELECT COUNT(*) FROM ${DatabaseConstants.meterReadingsTable}',
      );
      return Sqflite.firstIntValue(result) ?? 0;
    } catch (e) {
      Logger.error('Failed to get meter readings count: $e');
      return 0;
    }
  }

  @override
  Future<List<MeterReading>> getInvalidMeterReadings() async {
    try {
      final db = await _databaseHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        DatabaseConstants.meterReadingsTable,
        where: '(status = ? OR (status IS NULL AND is_valid = ?))',
        whereArgs: [EntryStatus.invalid.value, 0],
        orderBy: 'date DESC',
      );

      return List.generate(maps.length, (i) {
        return MeterReading.fromMap(maps[i]);
      });
    } catch (e) {
      Logger.error('Failed to get invalid meter readings: $e');
      return [];
    }
  }

  @override
  Future<bool> validateMeterReading(MeterReading meterReading) async {
    try {
      final validator = serviceLocator<MeterReadingValidator>();
      return await validator.validateMeterReading(meterReading);
    } catch (e) {
      Logger.error('Failed to validate meter reading: $e');
      return true; // Default to valid in case of error
    }
  }

  /// Clean up orphaned per-reading average for deleted meter reading
  Future<void> _cleanupPerReadingAverage(int meterReadingId) async {
    try {
      final perReadingAverageRepository =
          serviceLocator<PerReadingAverageRepository>();
      await perReadingAverageRepository
          .deletePerReadingAverageByMeterReadingId(meterReadingId);
      Logger.info(
          'MeterReadingRepository: Cleaned up per-reading average for meter reading $meterReadingId');
    } catch (e) {
      Logger.error(
          'Failed to cleanup per-reading average for meter reading $meterReadingId: $e');
    }
  }

  /// Update averages asynchronously without blocking the main operation
  void _updateAveragesAsync() {
    // Skip if we're in a bulk operation to avoid excessive calculations
    if (BulkOperationContext.isBulkOperation) {
      return;
    }

    try {
      final averageService = serviceLocator<AverageService>();
      // Fire and forget - don't await to avoid blocking
      // AverageService will fire EventType.dataUpdated when calculation completes
      averageService.updateAverages().catchError((error) {
        Logger.error('Failed to update averages: $error');
      });
    } catch (e) {
      Logger.error('Failed to get AverageService: $e');
    }
  }

  /// Trigger validation after adding a meter reading
  void _triggerValidationAsync(int meterReadingId) {
    try {
      final validationTriggerService =
          serviceLocator<ValidationTriggerService>();
      // Fire and forget - don't await to avoid blocking
      validationTriggerService
          .validateAfterAdd(meterReadingId)
          .catchError((error) {
        Logger.error('Failed to trigger validation after add: $error');
      });
    } catch (e) {
      Logger.error('Failed to get ValidationTriggerService: $e');
    }
  }

  /// Trigger validation after updating a meter reading
  void _triggerValidationUpdateAsync(int meterReadingId) {
    try {
      final validationTriggerService =
          serviceLocator<ValidationTriggerService>();
      // Fire and forget - don't await to avoid blocking
      validationTriggerService
          .validateAfterUpdate(meterReadingId)
          .catchError((error) {
        Logger.error('Failed to trigger validation after update: $error');
      });
    } catch (e) {
      Logger.error('Failed to get ValidationTriggerService: $e');
    }
  }

  /// Trigger validation after deleting a meter reading
  void _triggerValidationDeleteAsync(DateTime deletedDate) {
    try {
      final validationTriggerService =
          serviceLocator<ValidationTriggerService>();
      // Fire and forget - don't await to avoid blocking
      validationTriggerService
          .validateAfterDelete(deletedDate)
          .catchError((error) {
        Logger.error('Failed to trigger validation after delete: $error');
      });
    } catch (e) {
      Logger.error('Failed to get ValidationTriggerService: $e');
    }
  }
}
