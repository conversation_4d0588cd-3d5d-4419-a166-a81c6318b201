/// Enum representing different date format options
enum DateFormat {
  /// Format: DD-MM-YYYY (e.g., 11-05-2025)
  ddMmYyyy,
  
  /// Format: MM-DD-YYYY (e.g., 05-11-2025)
  mmDdYyyy,
  
  /// Format: YYYY-MM-DD (e.g., 2025-05-11)
  yyyyMmDd;
  
  /// Get the string representation of the date format
  String get formatString {
    switch (this) {
      case DateFormat.ddMmYyyy:
        return 'dd-MM-yyyy';
      case DateFormat.mmDdYyyy:
        return 'MM-dd-yyyy';
      case DateFormat.yyyyMmDd:
        return 'yyyy-MM-dd';
    }
  }
  
  /// Get a display example of the date format
  String get example {
    switch (this) {
      case DateFormat.ddMmYyyy:
        return '11-05-2025';
      case DateFormat.mmDdYyyy:
        return '05-11-2025';
      case DateFormat.yyyyMmDd:
        return '2025-05-11';
    }
  }
  
  /// Get a user-friendly display name
  String get displayName {
    switch (this) {
      case DateFormat.ddMmYyyy:
        return 'DD-MM-YYYY (11-05-2025)';
      case DateFormat.mmDdYyyy:
        return 'MM-DD-YYYY (05-11-2025)';
      case DateFormat.yyyyMmDd:
        return 'YYYY-MM-DD (2025-05-11)';
    }
  }
  
  /// Parse a string to get the corresponding DateFormat
  static DateFormat fromString(String value) {
    switch (value) {
      case 'dd-MM-yyyy':
        return DateFormat.ddMmYyyy;
      case 'MM-dd-yyyy':
        return DateFormat.mmDdYyyy;
      case 'yyyy-MM-dd':
        return DateFormat.yyyyMmDd;
      default:
        return DateFormat.ddMmYyyy; // Default
    }
  }
}

/// Enum representing date information display options
enum DateInfoType {
  /// Show date only
  dateOnly,
  
  /// Show date and time
  dateAndTime;
  
  /// Get a user-friendly display name
  String get displayName {
    switch (this) {
      case DateInfoType.dateOnly:
        return 'Date only (Example: 11-05-2025)';
      case DateInfoType.dateAndTime:
        return 'Date and time (Example: 11-05-2025 16:39)';
    }
  }
  
  /// Parse a string to get the corresponding DateInfoType
  static DateInfoType fromString(String value) {
    switch (value) {
      case 'date_only':
        return DateInfoType.dateOnly;
      case 'date_and_time':
        return DateInfoType.dateAndTime;
      default:
        return DateInfoType.dateAndTime; // Default
    }
  }
  
  /// Get the string representation of the date info type
  String get stringValue {
    switch (this) {
      case DateInfoType.dateOnly:
        return 'date_only';
      case DateInfoType.dateAndTime:
        return 'date_and_time';
    }
  }
}
