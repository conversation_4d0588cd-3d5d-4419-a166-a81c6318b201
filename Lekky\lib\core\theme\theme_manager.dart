import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../constants/preference_keys.dart';
import '../shared/models/theme_mode.dart';

/// Singleton theme manager for robust theme switching
class ThemeManager extends ChangeNotifier {
  static ThemeManager? _instance;
  static ThemeManager get instance => _instance ??= ThemeManager._();
  
  ThemeManager._();

  /// Current theme mode
  ThemeMode _currentTheme = ThemeMode.system;
  
  /// Whether the manager is initialized
  bool _isInitialized = false;
  
  /// SharedPreferences instance
  SharedPreferences? _prefs;

  /// Get current theme mode
  ThemeMode get currentTheme => _currentTheme;
  
  /// Whether the manager is initialized
  bool get isInitialized => _isInitialized;

  /// Initialize the theme manager
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      _prefs = await SharedPreferences.getInstance();
      await _loadThemeMode();
      _isInitialized = true;
    } catch (e) {
      debugPrint('ThemeManager initialization failed: $e');
      _currentTheme = ThemeMode.system;
      _isInitialized = true;
    }
  }

  /// Load theme mode from preferences
  Future<void> _loadThemeMode() async {
    try {
      final themeModeString = _prefs?.getString(PreferenceKeys.themeMode);
      if (themeModeString != null) {
        final appThemeMode = AppThemeMode.fromString(themeModeString);
        _currentTheme = _convertToFlutterThemeMode(appThemeMode);
      } else {
        _currentTheme = ThemeMode.system;
      }
    } catch (e) {
      debugPrint('Failed to load theme mode: $e');
      _currentTheme = ThemeMode.system;
    }
  }

  /// Set theme mode with immediate application and persistence
  Future<void> setThemeMode(AppThemeMode mode) async {
    try {
      // Convert and apply immediately
      final flutterThemeMode = _convertToFlutterThemeMode(mode);
      _currentTheme = flutterThemeMode;
      
      // Notify listeners for immediate UI update
      notifyListeners();
      
      // Persist to preferences
      await _saveThemeMode(mode);
    } catch (e) {
      debugPrint('Failed to set theme mode: $e');
      // Revert on error
      await _loadThemeMode();
      notifyListeners();
    }
  }

  /// Save theme mode to preferences
  Future<void> _saveThemeMode(AppThemeMode mode) async {
    try {
      if (_prefs == null) {
        _prefs = await SharedPreferences.getInstance();
      }
      await _prefs!.setString(PreferenceKeys.themeMode, mode.toString());
    } catch (e) {
      debugPrint('Failed to save theme mode: $e');
      throw e;
    }
  }

  /// Convert AppThemeMode to Flutter ThemeMode
  ThemeMode _convertToFlutterThemeMode(AppThemeMode mode) {
    switch (mode) {
      case AppThemeMode.light:
        return ThemeMode.light;
      case AppThemeMode.dark:
        return ThemeMode.dark;
      case AppThemeMode.system:
        return ThemeMode.system;
    }
  }

  /// Convert Flutter ThemeMode to AppThemeMode
  AppThemeMode _convertToAppThemeMode(ThemeMode mode) {
    switch (mode) {
      case ThemeMode.light:
        return AppThemeMode.light;
      case ThemeMode.dark:
        return AppThemeMode.dark;
      case ThemeMode.system:
        return AppThemeMode.system;
    }
  }

  /// Get current theme mode as AppThemeMode
  AppThemeMode getCurrentAppThemeMode() {
    return _convertToAppThemeMode(_currentTheme);
  }

  /// Refresh theme from preferences (for error recovery)
  Future<void> refresh() async {
    try {
      await _loadThemeMode();
      notifyListeners();
    } catch (e) {
      debugPrint('Failed to refresh theme: $e');
    }
  }
}
