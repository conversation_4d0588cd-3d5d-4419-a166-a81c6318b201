// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'notification_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

// ignore_for_file: avoid_private_typedef_functions, non_constant_identifier_names, subtype_of_sealed_class, invalid_use_of_internal_member, unused_element, constant_identifier_names, unnecessary_raw_strings, library_private_types_in_public_api

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

String _$NotificationHash() => r'7c283da869b19999d9453c9017110c9d5772a8e5';

/// Notification provider with comprehensive notification management
///
/// Copied from [Notification].
final notificationProvider =
    AutoDisposeAsyncNotifierProvider<Notification, NotificationState>(
  Notification.new,
  name: r'notificationProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$NotificationHash,
);
typedef NotificationRef
    = AutoDisposeAsyncNotifierProviderRef<NotificationState>;

abstract class _$Notification
    extends AutoDisposeAsyncNotifier<NotificationState> {
  @override
  FutureOr<NotificationState> build();
}

String _$notificationRepositoryHash() =>
    r'43796cb8e2a7bdc64d7f69f7e0bf91a3fa94d294';

/// Notification repository provider
///
/// Copied from [notificationRepository].
final notificationRepositoryProvider =
    AutoDisposeProvider<NotificationRepository>(
  notificationRepository,
  name: r'notificationRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$notificationRepositoryHash,
);
typedef NotificationRepositoryRef
    = AutoDisposeProviderRef<NotificationRepository>;
String _$notificationServiceHash() =>
    r'93e1300d036d8ee570ca1236ac08d958f02aed38';

/// Notification service provider
///
/// Copied from [notificationService].
final notificationServiceProvider = AutoDisposeProvider<NotificationService>(
  notificationService,
  name: r'notificationServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$notificationServiceHash,
);
typedef NotificationServiceRef = AutoDisposeProviderRef<NotificationService>;
