// File: lib/features/cost/data/cost_repository.dart
import '../../../core/utils/logger.dart';
import '../../../core/services/preference_service.dart';
import '../../../core/di/service_locator.dart';
import '../../averages/domain/services/average_service.dart';
import '../../averages/domain/repositories/per_reading_average_repository.dart';
import '../../meter_readings/domain/models/meter_reading.dart';
import '../../meter_readings/domain/repositories/meter_reading_repository.dart';
import '../../top_ups/domain/models/top_up.dart';
import '../../top_ups/domain/repositories/top_up_repository.dart';
import '../domain/models/cost_period.dart';
import '../domain/models/cost_result.dart';
import '../presentation/models/chart_data.dart';

/// Repository for cost-related operations
class CostRepository {
  final MeterReadingRepository _meterReadingRepository;
  final TopUpRepository _topUpRepository;
  final PreferenceService _preferenceService;
  final PerReadingAverageRepository _perReadingAverageRepository;

  /// Constructor
  CostRepository({
    MeterReadingRepository? meterReadingRepository,
    TopUpRepository? topUpRepository,
    PreferenceService? preferenceService,
    PerReadingAverageRepository? perReadingAverageRepository,
  })  : _meterReadingRepository =
            meterReadingRepository ?? serviceLocator<MeterReadingRepository>(),
        _topUpRepository = topUpRepository ?? serviceLocator<TopUpRepository>(),
        _preferenceService =
            preferenceService ?? serviceLocator<PreferenceService>(),
        _perReadingAverageRepository = perReadingAverageRepository ??
            serviceLocator<PerReadingAverageRepository>();

  /// Calculate historic cost using stored recent averages for time segments
  Future<double> calculateHistoricCostUsingStoredAverages(
      DateTime fromDate, DateTime toDate) async {
    try {
      // Get all meter readings and top-ups
      final List<MeterReading> meterReadings =
          await _meterReadingRepository.getAllMeterReadings();
      final List<TopUp> topUps = await _topUpRepository.getAllTopUps();

      if (meterReadings.length < 2) {
        Logger.warning(
            'CostRepository: Insufficient meter readings for calculation');
        return calculateUsingRawMeterReadings(fromDate, toDate);
      }

      // Sort meter readings by date
      final List<MeterReading> sortedReadings =
          List<MeterReading>.from(meterReadings)
            ..sort((a, b) => a.date.compareTo(b.date));

      // Find meter readings within and around the date range
      final List<MeterReading> readingsInRange = sortedReadings
          .where((reading) =>
              (reading.date.isAfter(fromDate) ||
                  reading.date.isAtSameMomentAs(fromDate)) &&
              (reading.date.isBefore(toDate) ||
                  reading.date.isAtSameMomentAs(toDate)))
          .toList();

      double totalCost = 0.0;

      if (readingsInRange.isEmpty) {
        // No meter readings in range - use recent average from next reading
        final nextReading = sortedReadings
            .where((reading) => reading.date.isAfter(toDate))
            .firstOrNull;

        if (nextReading != null) {
          final perReadingAverage = await _perReadingAverageRepository
              .getPerReadingAverageByMeterReadingId(nextReading.id!);

          if (perReadingAverage != null) {
            final days = toDate.difference(fromDate).inDays + 1;
            totalCost = perReadingAverage.recentAveragePerDay * days;

            Logger.info(
                'CostRepository: No readings in range - Using next reading average: ${perReadingAverage.recentAveragePerDay}/day × $days days = $totalCost');
          } else {
            Logger.warning(
                'CostRepository: No per-reading average found, using fallback');
            return calculateUsingRawMeterReadings(fromDate, toDate);
          }
        } else {
          Logger.warning(
              'CostRepository: No next reading found, using fallback');
          return calculateUsingRawMeterReadings(fromDate, toDate);
        }
      } else {
        // Calculate cost for each time segment
        totalCost = await _calculateSegmentedCost(
            fromDate, toDate, sortedReadings, readingsInRange, topUps);
      }

      Logger.info(
          'CostRepository: Historic cost calculation complete - Total: $totalCost');

      return totalCost > 0 ? totalCost : 0.0;
    } catch (e) {
      Logger.error(
          'CostRepository: Failed to calculate historic cost using stored averages: $e');
      return calculateUsingRawMeterReadings(fromDate, toDate);
    }
  }

  /// Calculate cost for time segments using stored recent averages
  Future<double> _calculateSegmentedCost(
      DateTime fromDate,
      DateTime toDate,
      List<MeterReading> sortedReadings,
      List<MeterReading> readingsInRange,
      List<TopUp> topUps) async {
    double totalCost = 0.0;
    DateTime currentDate = fromDate;

    for (int i = 0; i < readingsInRange.length; i++) {
      final currentReading = readingsInRange[i];

      // Calculate cost for period before this meter reading
      if (currentDate.isBefore(currentReading.date)) {
        final perReadingAverage = await _perReadingAverageRepository
            .getPerReadingAverageByMeterReadingId(currentReading.id!);

        if (perReadingAverage != null) {
          final days = currentReading.date.difference(currentDate).inDays;
          if (days > 0) {
            final segmentCost = perReadingAverage.recentAveragePerDay * days;
            totalCost += segmentCost;

            Logger.info(
                'CostRepository: Before reading segment - ${perReadingAverage.recentAveragePerDay}/day × $days days = $segmentCost');
          }
        }
      }

      // Calculate cost between this reading and next reading (if exists)
      if (i < readingsInRange.length - 1) {
        final nextReading = readingsInRange[i + 1];
        final meterDecrease = currentReading.value - nextReading.value;

        // Add top-ups between these readings
        double topUpsBetween = 0.0;
        for (var topUp in topUps) {
          final topUpDate = topUp.date;
          if (topUpDate.isAfter(currentReading.date) &&
              topUpDate.isBefore(nextReading.date)) {
            topUpsBetween += topUp.amount;
          }
        }

        final betweenCost = meterDecrease + topUpsBetween;
        totalCost += betweenCost;

        Logger.info(
            'CostRepository: Between readings segment - Meter decrease: $meterDecrease, Top-ups: $topUpsBetween, Total: $betweenCost');

        currentDate = nextReading.date;
      } else {
        // Last reading - handle period after it
        currentDate = currentReading.date;
      }
    }

    // Calculate cost for period after last meter reading
    if (currentDate.isBefore(toDate)) {
      final lastReading = readingsInRange.last;
      final nextReading = sortedReadings
          .where((reading) => reading.date.isAfter(lastReading.date))
          .firstOrNull;

      if (nextReading != null) {
        final perReadingAverage = await _perReadingAverageRepository
            .getPerReadingAverageByMeterReadingId(nextReading.id!);

        if (perReadingAverage != null) {
          final days = toDate.difference(currentDate).inDays;
          if (days > 0) {
            final segmentCost = perReadingAverage.recentAveragePerDay * days;
            totalCost += segmentCost;

            Logger.info(
                'CostRepository: After reading segment - ${perReadingAverage.recentAveragePerDay}/day × $days days = $segmentCost');
          }
        }
      }
    }

    // Add any top-ups within the date range that weren't included between readings
    double additionalTopUps = 0.0;
    for (var topUp in topUps) {
      final topUpDate = topUp.date;
      if ((topUpDate.isAfter(fromDate) ||
              topUpDate.isAtSameMomentAs(fromDate)) &&
          (topUpDate.isBefore(toDate) || topUpDate.isAtSameMomentAs(toDate))) {
        // Check if this top-up was already included in between-readings calculation
        bool alreadyIncluded = false;
        for (int i = 0; i < readingsInRange.length - 1; i++) {
          final currentReading = readingsInRange[i];
          final nextReading = readingsInRange[i + 1];
          if (topUpDate.isAfter(currentReading.date) &&
              topUpDate.isBefore(nextReading.date)) {
            alreadyIncluded = true;
            break;
          }
        }

        if (!alreadyIncluded) {
          additionalTopUps += topUp.amount;
        }
      }
    }

    totalCost += additionalTopUps;
    if (additionalTopUps > 0) {
      Logger.info(
          'CostRepository: Additional top-ups not between readings: $additionalTopUps');
    }

    return totalCost;
  }

  /// Fallback calculation using raw meter readings
  Future<double> calculateUsingRawMeterReadings(
      DateTime fromDate, DateTime toDate) async {
    try {
      // Fallback to total average calculation
      final averageService = serviceLocator<AverageService>();
      final result = await averageService.getAverages();
      final days = toDate.difference(fromDate).inDays + 1;
      return result.totalAverage * days;
    } catch (e) {
      Logger.error('Failed to calculate using raw meter readings: $e');
      return 0.0;
    }
  }

  /// Calculate the cost for a specific period
  Future<CostResult> calculateCostForPeriod(
      CostPeriod period, DateTime? fromDate, DateTime? toDate) async {
    try {
      // Get all meter readings and top-ups
      final meterReadings = await _meterReadingRepository.getAllMeterReadings();
      final topUps = await _topUpRepository.getAllTopUps();

      Logger.info(
          'CostRepository: Found ${meterReadings.length} meter readings and ${topUps.length} top-ups');

      double averageUsage = 0.0; // No default usage when no data
      double? topUpAmount;
      DateTime? topUpDate;

      // Get the meter unit from preferences
      final meterUnit = _preferenceService.currencySymbol;

      // Calculate the cost for the period
      double costPerPeriod;

      // Determine effective date range
      final effectiveFromDate =
          fromDate ?? DateTime.now().subtract(Duration(days: period.days));
      final effectiveToDate = toDate ?? DateTime.now();

      // Get average usage from AverageService
      try {
        final averageService = serviceLocator<AverageService>();
        final result = await averageService.getAverages();
        averageUsage = result.totalAverage;

        Logger.info(
            'CostRepository: Got average usage from ${result.fromCache ? "cache" : "calculation"}: $averageUsage per day');
      } catch (e) {
        Logger.error('CostRepository: Failed to get averages from service: $e');

        // Fallback to direct calculation if service fails
        if (meterReadings.length >= 2) {
          final sortedReadings = List.from(meterReadings)
            ..sort((a, b) => a.date.compareTo(b.date));

          final firstReading = sortedReadings.first;
          final lastReading = sortedReadings.last;
          final totalDays =
              lastReading.date.difference(firstReading.date).inDays;

          if (totalDays > 0) {
            // Calculate total top-ups between first and last readings
            double totalTopUps = 0;
            for (var topUp in topUps) {
              if (topUp.date.isAfter(firstReading.date) &&
                  topUp.date.isBefore(lastReading.date)) {
                totalTopUps += topUp.amount;
              }
            }

            // Total usage = (first reading - last reading + top-ups)
            final totalUsage =
                firstReading.value - lastReading.value + totalTopUps;

            if (totalUsage > 0) {
              averageUsage = totalUsage / totalDays;
              Logger.info(
                  'CostRepository: Fallback calculation - average usage: $averageUsage per day');
            }
          }
        }
      }

      // Calculate cost based on period
      int periodDays;
      if (period == CostPeriod.custom && fromDate != null && toDate != null) {
        // For custom periods, use stored averages for most accurate results
        costPerPeriod =
            await calculateHistoricCostUsingStoredAverages(fromDate, toDate);
        periodDays = toDate.difference(fromDate).inDays + 1;
        averageUsage = periodDays > 0 ? costPerPeriod / periodDays : 0.0;
      } else {
        // For standard periods, use predefined days
        periodDays = period.days;
        costPerPeriod = averageUsage * periodDays;
      }
      Logger.info(
          'CostRepository: Final cost calculation - Average usage: $averageUsage, Period days: $periodDays, Cost per period: $costPerPeriod');

      // Get the latest top-up information
      if (topUps.isNotEmpty) {
        final latestTopUp = topUps.first; // Already sorted by date DESC
        topUpAmount = latestTopUp.amount;
        topUpDate = latestTopUp.date;
      }

      // Create the cost result with all information
      return CostResult(
        averageUsage: averageUsage,
        costPerPeriod: costPerPeriod,
        period: period,
        meterUnit: meterUnit,
        topUpAmount: topUpAmount,
        topUpDate: topUpDate,
        fromDate: effectiveFromDate,
        toDate: effectiveToDate,
      );
    } catch (e) {
      Logger.error('Failed to calculate cost for period: $e');

      // Return a default cost result with zero values
      return CostResult(
        averageUsage: 0.0,
        costPerPeriod: 0.0,
        period: period,
        meterUnit: _preferenceService.currencySymbol,
        fromDate: fromDate,
        toDate: toDate,
      );
    }
  }

  /// Get the meter unit
  Future<String> getMeterUnit() async {
    return _preferenceService.currencySymbol;
  }

  /// Get the total average usage using AverageService with fallback
  Future<double> getTotalAverageUsage() async {
    try {
      // Try to use AverageService first
      final averageService = serviceLocator<AverageService>();
      final result = await averageService.getAverages();

      Logger.info(
          'CostRepository: Got total average from ${result.fromCache ? "cache" : "calculation"}: ${result.totalAverage}');
      return result.totalAverage;
    } catch (e) {
      Logger.error('CostRepository: Failed to get averages from service: $e');

      // Fallback to direct calculation
      return await _calculateTotalAverageFallback();
    }
  }

  /// Fallback method for calculating total average
  Future<double> _calculateTotalAverageFallback() async {
    try {
      Logger.info('CostRepository: Using fallback calculation');

      final meterReadings = await _meterReadingRepository.getAllMeterReadings();
      final topUps = await _topUpRepository.getAllTopUps();

      if (meterReadings.length < 2) {
        return 0.0; // No usage when insufficient data
      }

      // Calculate average usage from meter readings
      final sortedReadings = List.from(meterReadings)
        ..sort((a, b) => a.date.compareTo(b.date));

      final firstReading = sortedReadings.first;
      final lastReading = sortedReadings.last;
      final totalDays = lastReading.date.difference(firstReading.date).inDays;

      if (totalDays > 0) {
        // Calculate total top-ups between first and last readings
        double totalTopUps = 0;
        for (var topUp in topUps) {
          if (topUp.date.isAfter(firstReading.date) &&
              topUp.date.isBefore(lastReading.date)) {
            totalTopUps += topUp.amount;
          }
        }

        // Total usage = (first reading - last reading + top-ups)
        final totalUsage = firstReading.value - lastReading.value + totalTopUps;

        if (totalUsage > 0) {
          return totalUsage / totalDays;
        }
      }

      return 0.0; // No usage when calculation fails
    } catch (e) {
      Logger.error('CostRepository: Fallback calculation failed: $e');
      return 0.0;
    }
  }

  /// Get first meter reading date
  Future<DateTime?> getFirstMeterReadingDate() async {
    try {
      final meterReadings = await _meterReadingRepository.getAllMeterReadings();
      if (meterReadings.isEmpty) return null;

      final sortedReadings = List.from(meterReadings)
        ..sort((a, b) => a.date.compareTo(b.date));
      return sortedReadings.first.date;
    } catch (e) {
      Logger.error('Failed to get first meter reading date: $e');
      return null;
    }
  }

  /// Get last meter reading date
  Future<DateTime?> getLastMeterReadingDate() async {
    try {
      final meterReadings = await _meterReadingRepository.getAllMeterReadings();
      if (meterReadings.isEmpty) return null;

      final sortedReadings = List.from(meterReadings)
        ..sort((a, b) => b.date.compareTo(a.date));
      return sortedReadings.first.date;
    } catch (e) {
      Logger.error('Failed to get last meter reading date: $e');
      return null;
    }
  }

  /// Get previous meter reading date (second-to-last chronologically)
  Future<DateTime?> getPreviousMeterReadingDate() async {
    try {
      final meterReadings = await _meterReadingRepository.getAllMeterReadings();
      if (meterReadings.length < 2) return null;

      final sortedReadings = List.from(meterReadings)
        ..sort((a, b) => b.date.compareTo(a.date));
      return sortedReadings[1].date;
    } catch (e) {
      Logger.error('Failed to get previous meter reading date: $e');
      return null;
    }
  }

  /// Check if there are sufficient meter readings (at least 2)
  Future<bool> hasSufficientMeterReadings() async {
    try {
      final meterReadings = await _meterReadingRepository.getAllMeterReadings();
      return meterReadings.length >= 2;
    } catch (e) {
      Logger.error('Failed to check sufficient meter readings: $e');
      return false;
    }
  }

  /// Get recent average chart data for visualization
  Future<List<ChartData>> getRecentAverageChartData(
      DateTime? fromDate, DateTime? toDate) async {
    try {
      List<dynamic> averages;

      if (fromDate != null && toDate != null) {
        // Get per-reading averages within date range
        averages = await _perReadingAverageRepository
            .getPerReadingAveragesForDateRange(fromDate, toDate);
      } else {
        // Get all per-reading averages
        averages =
            await _perReadingAverageRepository.getAllPerReadingAverages();
      }

      // Convert to ChartData format with cost calculation
      return averages
          .map((avg) => ChartData(
                date: avg.readingDate,
                usage: avg.recentAveragePerDay,
                cost: avg
                    .recentAveragePerDay, // Cost per day = usage per day (1:1 ratio)
              ))
          .toList();
    } catch (e) {
      Logger.error('Failed to get recent average chart data: $e');
      return [];
    }
  }

  /// Get recent average chart data for all averages (last 100)
  Future<List<ChartData>> getRecentAverageChartDataForAllAverages() async {
    try {
      // Get last 100 per-reading averages
      final averages =
          await _perReadingAverageRepository.getRecentPerReadingAverages(100);

      // Convert to ChartData format with cost calculation
      return averages
          .map((avg) => ChartData(
                date: avg.readingDate,
                usage: avg.recentAveragePerDay,
                cost: avg
                    .recentAveragePerDay, // Cost per day = usage per day (1:1 ratio)
              ))
          .toList();
    } catch (e) {
      Logger.error(
          'Failed to get recent average chart data for all averages: $e');
      return [];
    }
  }
}
