import 'package:flutter/material.dart';

/// A card that displays usage statistics
class UsageStatisticsCard extends StatelessWidget {
  /// Recent average daily usage
  final double? recentAverageDailyUsage;

  /// Total average daily usage
  final double? totalAverageDailyUsage;

  /// Currency symbol to use
  final String currencySymbol;

  /// Constructor
  const UsageStatisticsCard({
    Key? key,
    required this.recentAverageDailyUsage,
    required this.totalAverageDailyUsage,
    this.currencySymbol = '₦',
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with icon and title
            Row(
              children: [
                Icon(
                  Icons.show_chart,
                  color: theme.colorScheme.onSurface,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'Usage Statistics',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildStatisticItem(
                    context,
                    'Recent Avg.',
                    recentAverageDailyUsage,
                    Icons.trending_up,
                    theme.colorScheme.primary,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildStatisticItem(
                    context,
                    'Total Avg.',
                    totalAverageDailyUsage,
                    Icons.show_chart,
                    theme.colorScheme.secondary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              'Based on your usage patterns',
              style: TextStyle(
                fontSize: 12,
                color: theme.colorScheme.onSurface.withOpacity(0.6),
                fontStyle: FontStyle.italic,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// Build a single statistic item
  Widget _buildStatisticItem(
    BuildContext context,
    String title,
    double? value,
    IconData icon,
    Color color,
  ) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                color: color,
                size: 16,
              ),
              const SizedBox(width: 4),
              Text(
                title,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: theme.colorScheme.onSurface,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            value != null
                ? '$currencySymbol${value.toStringAsFixed(2)}/day'
                : 'Not enough data',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.onSurface,
            ),
          ),
        ],
      ),
    );
  }
}
