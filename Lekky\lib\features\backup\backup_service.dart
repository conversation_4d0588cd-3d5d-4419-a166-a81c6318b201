// File: lib/features/backup/backup_service.dart
import 'dart:io';
import 'dart:typed_data';
import 'package:csv/csv.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:intl/intl.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import '../../core/models/meter_entry.dart';
import '../../core/utils/date_time_utils.dart';
import '../../core/utils/logger.dart';
import '../../core/utils/permission_helper.dart';
import '../../core/utils/result.dart';
import 'backup_errors.dart';

/// Service for backing up and restoring meter entries
class BackupService {
  /// Backup format version
  static const int BACKUP_FORMAT_VERSION = 101;

  /// Backup filename
  static const String BACKUP_FILENAME = 'lekky_export_101.csv';

  /// Permission helper
  final PermissionHelper _permissionHelper = PermissionHelper();

  /// Logger
  final logger = Logger('BackupService');

  /// Exports meter entries to a CSV file
  ///
  /// Returns a Result with the File on success or an AppError on failure
  /// The optional onComplete callback is called when the export is complete,
  /// regardless of success or failure
  Future<Result<File>> exportMeterEntries({
    required List<MeterEntry> entries,
    VoidCallback? onComplete,
  }) async {
    try {
      // Create CSV data with version header
      final csvData = [
        ['# Lekky v1.0.1 BackupFormat=$BACKUP_FORMAT_VERSION'],
        ['Date', 'Type', 'Amount'],
        ...entries.map((entry) => [
              // Use ISO 8601 format for better compatibility
              entry.timestamp.toIso8601String(),
              // Use numeric type codes: 0 = Meter Reading, 1 = Top Up
              entry.typeCode.toString(),
              entry.amountToppedUp > 0
                  ? entry.amountToppedUp.toString()
                  : entry.reading.toString(),
            ]),
      ];

      // Convert to CSV string
      final csv = const ListToCsvConverter().convert(csvData);

      // First try using the Storage Access Framework to save the file
      try {
        // Use FilePicker to let the user choose where to save the file
        final result = await FilePicker.platform.saveFile(
          dialogTitle: 'Save Backup File',
          fileName: BACKUP_FILENAME,
          type: FileType.custom,
          allowedExtensions: ['csv'],
        );

        if (result != null) {
          // User selected a location
          final file = File(result);
          await file.writeAsString(csv);
          logger.i('Data exported to $result using Storage Access Framework');

          // Call the onComplete callback if provided
          onComplete?.call();

          return Result.success(file);
        }
      } catch (safError) {
        // Log the error but continue with fallback method
        logger.w('Error using Storage Access Framework',
            details: safError.toString());
      }

      // If SAF failed or was cancelled, try the traditional method
      try {
        // Check if we have permission
        final hasPermission =
            await _permissionHelper.checkAndRequestStoragePermission();
        if (hasPermission) {
          final downloadsPath = await _getDownloadsPath();
          if (downloadsPath != null) {
            final path = '$downloadsPath/$BACKUP_FILENAME';
            final file = File(path);
            await file.writeAsString(csv);
            logger.i('Data exported to $path using traditional method');

            // Call the onComplete callback if provided
            onComplete?.call();

            return Result.success(file);
          }
        }
      } catch (traditionalError) {
        // Log the error but continue with fallback method
        logger.w('Error using traditional method',
            details: traditionalError.toString());
      }

      // If we get here, both methods failed, so we'll create a temporary file
      // and let the user share it
      final tempDir = await getTemporaryDirectory();
      final tempFile = File('${tempDir.path}/$BACKUP_FILENAME');
      await tempFile.writeAsString(csv);

      // Call the onComplete callback if provided
      onComplete?.call();

      return Result.failure(createBackupError(
        BackupErrorType.fileIOError,
        'Could not save to Downloads folder. Please use the Share option to save your backup.',
        details: {'tempFilePath': tempFile.path},
      ));
    } catch (e) {
      logger.e('Failed to export data', details: e.toString());
      // Call the onComplete callback if provided
      onComplete?.call();

      return Result.failure(createBackupError(
        BackupErrorType.fileIOError,
        'Could not save backup file',
        details: e,
      ));
    }
  }

  /// Imports meter entries from a CSV file
  ///
  /// Returns a Result with a list of MeterEntry objects on success or an AppError on failure
  Future<Result<List<MeterEntry>>> importMeterEntries(File csvFile) async {
    try {
      // Read file content
      final csvString = await csvFile.readAsString();

      // Check if the file is empty
      if (csvString.trim().isEmpty) {
        return Result.failure(createBackupError(
          BackupErrorType.emptyData,
          'The backup file is empty',
        ));
      }

      // Split the file into lines for better header parsing
      final lines = csvString.split('\n');
      if (lines.isEmpty) {
        return Result.failure(createBackupError(
          BackupErrorType.emptyData,
          'The backup file is empty',
        ));
      }

      // Check for version header
      final versionHeader = lines.first.trim();
      final versionRegex = RegExp(r'# Lekky v[\d\.]+\s+BackupFormat=(\d+)');
      final versionMatch = versionRegex.firstMatch(versionHeader);

      if (versionMatch == null) {
        return Result.failure(createBackupError(
          BackupErrorType.invalidFormat,
          'Invalid backup file format: missing version header',
        ));
      }

      final backupVersion = int.parse(versionMatch.group(1)!);
      // Allow importing from version 100 (text types) or 101 (numeric types)
      if (backupVersion != BACKUP_FORMAT_VERSION && backupVersion != 100) {
        return Result.failure(createBackupError(
          BackupErrorType.versionMismatch,
          'This backup was made with an incompatible version of Lekky',
          details: {
            'fileVersion': backupVersion,
            'currentVersion': BACKUP_FORMAT_VERSION
          },
        ));
      }

      // Convert CSV string to table
      final csvTable = const CsvToListConverter().convert(csvString);

      // Validate file format
      if (csvTable.isEmpty || csvTable.length <= 2) {
        return Result.failure(createBackupError(
          BackupErrorType.emptyData,
          'No meter entries found in backup file',
        ));
      }

      // Parse entries
      final entries = <MeterEntry>[];
      int successCount = 0;
      int errorCount = 0;

      for (int i = 2; i < csvTable.length; i++) {
        final row = csvTable[i];
        if (row.length >= 3) {
          try {
            final dateStr = row[0].toString();
            final type = row[1].toString();
            final amount = double.parse(row[2].toString());

            // Parse the date
            DateTime date;
            try {
              date = DateTime.parse(dateStr);
            } catch (dateError) {
              // Try alternative date formats if ISO 8601 fails
              try {
                // Try dd/MM/yyyy HH:mm format
                date = DateFormat('dd/MM/yyyy HH:mm').parse(dateStr);
              } catch (e) {
                // Try dd-MM-yyyy HH:mm format
                date = DateFormat('dd-MM-yyyy HH:mm').parse(dateStr);
              }
            }

            // Handle both numeric type codes and legacy text types
            MeterEntry entry;

            // Check if type is a numeric code
            if (type == '0' || type == '1') {
              // Use numeric type code (0 = Meter Reading, 1 = Top Up)
              final typeCode = int.parse(type);
              entry = MeterEntry.fromTypeCodeAndAmount(
                id: null,
                typeCode: typeCode,
                amount: amount,
                timestamp: date,
                // No averages - they will be recalculated
                shortAverageAfterTopUp: null,
                totalAverageUpToThisPoint: null,
              );
            } else {
              // Legacy text type format
              entry = MeterEntry(
                id: null,
                reading: type.toLowerCase() == 'meter reading' ? amount : 0,
                amountToppedUp: type.toLowerCase() == 'top-up' ? amount : 0,
                date: date,
                typeCode: type.toLowerCase() == 'meter reading' ? 0 : 1,
                // No averages - they will be recalculated
                shortAverageAfterTopUp: null,
                totalAverageUpToThisPoint: null,
              );
            }
            entries.add(entry);
            successCount++;
          } catch (e) {
            // Skip invalid rows but log the error with detailed information
            errorCount++;
            logger.e(
              'Error parsing row',
              details: {
                'row': row,
                'dateStr': row[0].toString(),
                'type': row[1].toString(),
                'amount': row[2].toString(),
                'error': e.toString(),
              },
            );
          }
        }
      }

      if (entries.isEmpty) {
        return Result.failure(createBackupError(
          BackupErrorType.parseError,
          'Could not parse any valid entries from the backup file',
        ));
      }

      // Sort entries by timestamp to ensure chronological order
      entries.sort((a, b) => a.timestamp.compareTo(b.timestamp));

      logger.i(
          'Successfully imported ${entries.length} entries (Errors: $errorCount)');
      return Result.success(entries);
    } catch (e) {
      logger.e('Failed to import data', details: e.toString());
      return Result.failure(createBackupError(
        BackupErrorType.unknown,
        'An unexpected error occurred while importing data',
        details: e,
      ));
    }
  }

  /// Picks a backup file using the file picker
  ///
  /// Returns a Result with the File on success or an AppError on failure
  Future<Result<File>> pickBackupFile() async {
    try {
      // Use FilePicker to let the user choose a file
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['csv'],
        dialogTitle: 'Select Backup File',
      );

      if (result == null || result.files.isEmpty) {
        return Result.failure(createBackupError(
          BackupErrorType.userCancelled,
          'No file selected',
        ));
      }

      final path = result.files.first.path;
      if (path == null) {
        return Result.failure(createBackupError(
          BackupErrorType.fileIOError,
          'Could not get file path',
        ));
      }

      final file = File(path);
      if (!await file.exists()) {
        return Result.failure(createBackupError(
          BackupErrorType.fileIOError,
          'File does not exist',
        ));
      }

      return Result.success(file);
    } catch (e) {
      logger.e('Failed to pick backup file', details: e.toString());
      return Result.failure(createBackupError(
        BackupErrorType.unknown,
        'An unexpected error occurred while picking a backup file',
        details: e,
      ));
    }
  }

  /// Checks if a backup file exists
  Future<bool> backupFileExists() async {
    try {
      // First try to check using the traditional method
      try {
        // Check if we have permission
        final hasPermission =
            await _permissionHelper.checkAndRequestStoragePermission();
        if (hasPermission) {
          final downloadsPath = await _getDownloadsPath();
          if (downloadsPath != null) {
            final path = '$downloadsPath/$BACKUP_FILENAME';
            final file = File(path);
            final exists = await file.exists();
            if (exists) {
              return true;
            }
          }
        }
      } catch (e) {
        logger.w('Error checking for backup file using traditional method',
            details: e.toString());
      }

      // If traditional method failed, return false
      return false;
    } catch (e) {
      logger.e('Failed to check if backup file exists', details: e.toString());
      return false;
    }
  }

  /// Gets the path to the backup file
  Future<String?> getBackupFilePath() async {
    try {
      // First try to get the path using the traditional method
      try {
        // Check if we have permission
        final hasPermission =
            await _permissionHelper.checkAndRequestStoragePermission();
        if (hasPermission) {
          final downloadsPath = await _getDownloadsPath();
          if (downloadsPath != null) {
            final path = '$downloadsPath/$BACKUP_FILENAME';
            final file = File(path);
            if (await file.exists()) {
              return path;
            }
          }
        }
      } catch (e) {
        logger.w('Error getting backup file path using traditional method',
            details: e.toString());
      }

      // If traditional method failed, return null
      return null;
    } catch (e) {
      logger.e('Failed to get backup file path', details: e.toString());
      return null;
    }
  }

  /// Gets the path to the Downloads folder
  Future<String?> _getDownloadsPath() async {
    try {
      // For Android 10+ (API level 29+)
      if (Platform.isAndroid) {
        final directory = await getExternalStorageDirectory();
        if (directory != null) {
          // Navigate up to the external storage root
          final externalDir = directory.path.split('/Android')[0];
          final downloadsDir = '$externalDir/Download';

          // Create the directory if it doesn't exist
          final dir = Directory(downloadsDir);
          if (!await dir.exists()) {
            await dir.create(recursive: true);
          }

          return downloadsDir;
        }
      }

      // For iOS and fallback
      final directory = await getApplicationDocumentsDirectory();
      return directory.path;
    } catch (e) {
      logger.e('Error getting downloads path', details: e.toString());
      return null;
    }
  }
}
