import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../controllers/settings_controller.dart';
import '../../../../core/shared/widgets/settings_section_header.dart';

/// Notification Types settings screen
class NotificationTypesScreen extends StatelessWidget {
  /// Constructor
  const NotificationTypesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Notification Types'),
        backgroundColor: const Color(0xFF424242),
        foregroundColor: Colors.white,
      ),
      body: Consumer<SettingsController>(
        builder: (context, controller, _) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Card(
                  margin: const EdgeInsets.all(8.0),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12.0),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SettingsSectionHeader(
                          title: 'Notification Types',
                          description:
                              'Choose which notifications you want to receive.',
                          icon: Icons.notifications_active,
                        ),
                        const SizedBox(height: 16),

                        // Low Balance Alerts
                        SwitchListTile(
                          title: const Text('Low Balance Alerts'),
                          subtitle: const Text(
                              'Get notified when you have less than 24 hours of credit remaining'),
                          value: controller.lowBalanceAlertsEnabled,
                          onChanged: (value) {
                            controller.updateLowBalanceAlertsEnabled(value);
                          },
                        ),

                        const Divider(),

                        // Time to Top Up Alerts
                        SwitchListTile(
                          title: const Text('Time to Top Up Alerts'),
                          subtitle: const Text(
                              'Get notified when your alert threshold will be reached in your specified days in advance'),
                          value: controller.timeToTopUpAlertsEnabled,
                          onChanged: (value) {
                            controller.updateTimeToTopUpAlertsEnabled(value);
                          },
                        ),

                        const Divider(),

                        // Invalid Record Alerts
                        SwitchListTile(
                          title: const Text('Invalid Record Alerts'),
                          subtitle: const Text(
                              'Get notified when there\'s an issue with your meter readings.'),
                          value: controller.invalidRecordAlertsEnabled,
                          onChanged: (value) {
                            controller.updateInvalidRecordAlertsEnabled(value);
                          },
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
