import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../controllers/settings_controller.dart';
import '../../../../core/shared/widgets/settings_section_header.dart';

/// Time Display settings screen
class TimeDisplayScreen extends StatelessWidget {
  /// Constructor
  const TimeDisplayScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Time Display'),
        backgroundColor: const Color(0xFF424242),
        foregroundColor: Colors.white,
      ),
      body: Consumer<SettingsController>(
        builder: (context, controller, _) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Time display card
                Card(
                  margin: const EdgeInsets.all(8.0),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12.0),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SettingsSectionHeader(
                          title: 'Time Display',
                          description:
                              'Choose whether to show time alongside dates.',
                          icon: Icons.access_time,
                        ),
                        const SizedBox(height: 16),
                        const Text(
                          'Show time with date',
                          style: TextStyle(fontSize: 16),
                        ),
                        const SizedBox(height: 8),
                        const Text(
                          'When enabled, times will be shown alongside dates throughout the app.',
                          style: TextStyle(fontSize: 14),
                        ),
                        const SizedBox(height: 16),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text(
                              'Show time with date',
                              style: TextStyle(fontSize: 16),
                            ),
                            Switch(
                              value: controller.showTimeWithDate,
                              onChanged: (value) {
                                controller.updateShowTimeWithDate(value);
                              },
                              activeColor:
                                  Theme.of(context).colorScheme.primary,
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          controller.showTimeWithDate
                              ? 'Example: 11-05-2025 16:39'
                              : 'Example: 11-05-2025',
                          style: TextStyle(
                            fontSize: 14,
                            fontStyle: FontStyle.italic,
                            color: Theme.of(context).textTheme.bodySmall?.color,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
