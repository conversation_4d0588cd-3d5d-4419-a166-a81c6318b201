// File: lib/features/splash/presentation/screens/simplified_splash_screen.dart
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../../core/constants/app_constants.dart';
import '../widgets/simplified_splash_animation.dart';

/// The splash screen of the app
class SimplifiedSplashScreen extends StatefulWidget {
  const SimplifiedSplashScreen({super.key});

  @override
  State<SimplifiedSplashScreen> createState() => _SimplifiedSplashScreenState();
}

class _SimplifiedSplashScreenState extends State<SimplifiedSplashScreen> {
  String _statusMessage = 'Initializing...';

  @override
  void initState() {
    super.initState();
    _checkSetupStatus();
  }

  void _updateStatus(String message) {
    if (mounted) {
      setState(() {
        _statusMessage = message;
      });
    }
  }

  Future<void> _checkSetupStatus() async {
    try {
      // Simulate a delay for the splash screen
      await Future.delayed(const Duration(seconds: 2));

      if (!mounted) return;

      // Update status message
      _updateStatus('Checking permissions...');
      await Future.delayed(const Duration(seconds: 1));

      if (!mounted) return;

      // Update status message
      _updateStatus('Initializing...');
      await Future.delayed(const Duration(seconds: 1));

      if (!mounted) return;

      // Check if setup has been completed
      final prefs = await SharedPreferences.getInstance();
      final setupCompleted =
          prefs.getBool(AppConstants.keySetupCompleted) ?? false;
      final firstLaunch = prefs.getBool('first_launch') ?? true;

      if (!mounted) return;

      // For first-time users, update the first_launch flag
      if (firstLaunch) {
        await prefs.setBool('first_launch', false);
      }

      if (!mounted) return;

      // Navigate to the appropriate screen
      if (firstLaunch) {
        // First time launching the app, go to welcome screen
        Navigator.of(context).pushReplacementNamed(AppConstants.routeWelcome);
      } else if (!setupCompleted) {
        // Setup not completed, go to setup screen
        Navigator.of(context).pushReplacementNamed(AppConstants.routeSetup);
      } else {
        // Setup completed, go directly to home screen
        Navigator.of(context).pushReplacementNamed(AppConstants.routeHome);
      }
    } catch (e) {
      // Fallback to Welcome screen in case of error
      if (mounted) {
        Navigator.of(context).pushReplacementNamed(AppConstants.routeWelcome);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          color: Color(0xFF003087), // Deep blue to match welcome screen
        ),
        child: Stack(
          children: [
            const Center(
              child: SimplifiedSplashAnimation(),
            ),
            // Status message
            Positioned(
              bottom: 60,
              left: 0,
              right: 0,
              child: Center(
                child: Text(
                  _statusMessage,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
            // Debug overlay in debug mode only
            if (kDebugMode)
              Positioned(
                bottom: 10,
                left: 10,
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.7),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Text(
                    'Debug Build',
                    style: TextStyle(color: Colors.white, fontSize: 12),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
