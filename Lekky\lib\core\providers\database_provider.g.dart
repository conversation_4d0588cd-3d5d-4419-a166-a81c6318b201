// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'database_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

// ignore_for_file: avoid_private_typedef_functions, non_constant_identifier_names, subtype_of_sealed_class, invalid_use_of_internal_member, unused_element, constant_identifier_names, unnecessary_raw_strings, library_private_types_in_public_api

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

String _$databaseHelperHash() => r'8756a78fed012308a421b0f2a0f1b586cae71baf';

/// Database helper provider
///
/// Copied from [databaseHelper].
final databaseHelperProvider = AutoDisposeProvider<DatabaseHelper>(
  databaseHelper,
  name: r'databaseHelperProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$databaseHelperHash,
);
typedef DatabaseHelperRef = AutoDisposeProviderRef<DatabaseHelper>;
String _$meterReadingRepositoryHash() =>
    r'd5e3ef276804f4c7b91f81186f5dbddc1ba987c1';

/// Meter reading repository provider
///
/// Copied from [meterReadingRepository].
final meterReadingRepositoryProvider =
    AutoDisposeProvider<MeterReadingRepository>(
  meterReadingRepository,
  name: r'meterReadingRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$meterReadingRepositoryHash,
);
typedef MeterReadingRepositoryRef
    = AutoDisposeProviderRef<MeterReadingRepository>;
String _$topUpRepositoryHash() => r'165cb56478877413b3bf99414ed9e0b081127968';

/// Top-up repository provider
///
/// Copied from [topUpRepository].
final topUpRepositoryProvider = AutoDisposeProvider<TopUpRepository>(
  topUpRepository,
  name: r'topUpRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$topUpRepositoryHash,
);
typedef TopUpRepositoryRef = AutoDisposeProviderRef<TopUpRepository>;
