import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../controllers/settings_controller.dart';
import '../../../../core/shared/widgets/settings_toggle.dart';

/// Testing screen for developer options
class TestingScreen extends StatefulWidget {
  /// Constructor
  const TestingScreen({super.key});

  @override
  State<TestingScreen> createState() => _TestingScreenState();
}

class _TestingScreenState extends State<TestingScreen> {
  bool _debugModeEnabled = false;
  bool _showPerformanceOverlay = false;
  bool _showTestData = false;
  
  @override
  void initState() {
    super.initState();
    // TODO: Load values from settings controller
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Testing Options'),
        backgroundColor: const Color(0xFF424242),
        foregroundColor: Colors.white,
      ),
      body: Consumer<SettingsController>(
        builder: (context, controller, _) {
          return ListView(
            padding: const EdgeInsets.all(16.0),
            children: [
              // Warning card
              Card(
                margin: const EdgeInsets.only(bottom: 16.0),
                color: Colors.red.shade100,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Row(
                        children: [
                          Icon(Icons.warning, color: Colors.red),
                          SizedBox(width: 16),
                          Text(
                            'Developer Options',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.red,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      
                      // Warning message
                      const Text(
                        'These options are for testing and development purposes only. Enabling these options may affect app performance and stability.',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.red,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              
              // Debug options card
              Card(
                margin: const EdgeInsets.only(bottom: 16.0),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Row(
                        children: [
                          Icon(Icons.bug_report, color: Colors.blue),
                          SizedBox(width: 16),
                          Text(
                            'Debug Options',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      
                      // Debug mode toggle
                      SettingsToggle(
                        title: 'Enable Debug Mode',
                        description: 'Show debug information in the app',
                        value: _debugModeEnabled,
                        onChanged: (value) {
                          setState(() {
                            _debugModeEnabled = value;
                          });
                          // TODO: Save debug mode preference
                        },
                      ),
                      
                      const Divider(),
                      
                      // Performance overlay toggle
                      SettingsToggle(
                        title: 'Show Performance Overlay',
                        description: 'Display performance metrics on screen',
                        value: _showPerformanceOverlay,
                        onChanged: (value) {
                          setState(() {
                            _showPerformanceOverlay = value;
                          });
                          // TODO: Save performance overlay preference
                        },
                      ),
                    ],
                  ),
                ),
              ),
              
              // Test data card
              Card(
                margin: const EdgeInsets.only(bottom: 16.0),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Row(
                        children: [
                          Icon(Icons.data_array, color: Colors.blue),
                          SizedBox(width: 16),
                          Text(
                            'Test Data',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      
                      // Test data toggle
                      SettingsToggle(
                        title: 'Show Test Data',
                        description: 'Display test data in the app',
                        value: _showTestData,
                        onChanged: (value) {
                          setState(() {
                            _showTestData = value;
                          });
                          // TODO: Save test data preference
                        },
                      ),
                      
                      const SizedBox(height: 16),
                      
                      // Generate test data button
                      ElevatedButton.icon(
                        onPressed: () {
                          // TODO: Implement test data generation
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('Test data generated successfully'),
                              duration: Duration(seconds: 2),
                            ),
                          );
                        },
                        icon: const Icon(Icons.add),
                        label: const Text('Generate Test Data'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue,
                          foregroundColor: Colors.white,
                        ),
                      ),
                      
                      const SizedBox(height: 8),
                      
                      // Clear test data button
                      ElevatedButton.icon(
                        onPressed: () {
                          // TODO: Implement test data clearing
                          showDialog(
                            context: context,
                            builder: (context) => AlertDialog(
                              title: const Text('Clear Test Data'),
                              content: const Text(
                                'Are you sure you want to clear all test data? This action cannot be undone.',
                              ),
                              actions: [
                                TextButton(
                                  onPressed: () => Navigator.of(context).pop(),
                                  child: const Text('Cancel'),
                                ),
                                TextButton(
                                  onPressed: () {
                                    Navigator.of(context).pop();
                                    // TODO: Implement test data clearing
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      const SnackBar(
                                        content: Text('Test data cleared successfully'),
                                        duration: Duration(seconds: 2),
                                      ),
                                    );
                                  },
                                  child: const Text('Clear'),
                                ),
                              ],
                            ),
                          );
                        },
                        icon: const Icon(Icons.delete),
                        label: const Text('Clear Test Data'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.red,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              
              // Database tools card
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Row(
                        children: [
                          Icon(Icons.storage, color: Colors.blue),
                          SizedBox(width: 16),
                          Text(
                            'Database Tools',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      
                      // Reset database button
                      ElevatedButton.icon(
                        onPressed: () {
                          // TODO: Implement database reset
                          showDialog(
                            context: context,
                            builder: (context) => AlertDialog(
                              title: const Text('Reset Database'),
                              content: const Text(
                                'Are you sure you want to reset the database? This will delete all data and cannot be undone.',
                              ),
                              actions: [
                                TextButton(
                                  onPressed: () => Navigator.of(context).pop(),
                                  child: const Text('Cancel'),
                                ),
                                TextButton(
                                  onPressed: () {
                                    Navigator.of(context).pop();
                                    // TODO: Implement database reset
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      const SnackBar(
                                        content: Text('Database reset successfully'),
                                        duration: Duration(seconds: 2),
                                      ),
                                    );
                                  },
                                  child: const Text('Reset'),
                                ),
                              ],
                            ),
                          );
                        },
                        icon: const Icon(Icons.refresh),
                        label: const Text('Reset Database'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.red,
                          foregroundColor: Colors.white,
                        ),
                      ),
                      
                      const SizedBox(height: 8),
                      
                      // Optimize database button
                      ElevatedButton.icon(
                        onPressed: () {
                          // TODO: Implement database optimization
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('Database optimized successfully'),
                              duration: Duration(seconds: 2),
                            ),
                          );
                        },
                        icon: const Icon(Icons.speed),
                        label: const Text('Optimize Database'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          foregroundColor: Colors.white,
                        ),
                      ),
                      
                      const SizedBox(height: 8),
                      
                      // View database info button
                      ElevatedButton.icon(
                        onPressed: () {
                          // TODO: Implement database info view
                          showDialog(
                            context: context,
                            builder: (context) => AlertDialog(
                              title: const Text('Database Information'),
                              content: const Column(
                                mainAxisSize: MainAxisSize.min,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text('Database version: 1.0'),
                                  Text('Total entries: 0'),
                                  Text('Meter readings: 0'),
                                  Text('Top-ups: 0'),
                                  Text('Last updated: Never'),
                                ],
                              ),
                              actions: [
                                TextButton(
                                  onPressed: () => Navigator.of(context).pop(),
                                  child: const Text('Close'),
                                ),
                              ],
                            ),
                          );
                        },
                        icon: const Icon(Icons.info),
                        label: const Text('View Database Info'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}
