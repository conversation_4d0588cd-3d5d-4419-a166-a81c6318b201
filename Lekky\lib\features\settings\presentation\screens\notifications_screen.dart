import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../controllers/settings_controller.dart';
import '../../../../core/shared/widgets/settings_toggle.dart';
import '../../../notifications/presentation/dialogs/notification_dialog.dart';

/// Notifications settings screen
class NotificationsScreen extends StatelessWidget {
  /// Constructor
  const NotificationsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Alerts & Notifications'),
        backgroundColor: const Color(0xFF424242),
        foregroundColor: Colors.white,
      ),
      body: Consumer<SettingsController>(
        builder: (context, controller, _) {
          return ListView(
            padding: const EdgeInsets.all(16.0),
            children: [
              // Alert threshold card
              Card(
                margin: const EdgeInsets.only(bottom: 16.0),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Row(
                        children: [
                          Icon(Icons.warning, color: Colors.amber),
                          SizedBox(width: 16),
                          Text(
                            'Alert Threshold',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        'Set the threshold for low balance alerts. You will be notified when your balance falls below this amount.',
                        style: TextStyle(
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Alert threshold slider
                      Row(
                        children: [
                          Text('${controller.currencySymbol}0'),
                          Expanded(
                            child: Slider(
                              value: controller.alertThreshold,
                              min: 0,
                              max: 20,
                              divisions: 40,
                              label:
                                  '${controller.currencySymbol}${controller.alertThreshold.toStringAsFixed(2)}',
                              onChanged: (value) {
                                controller.updateAlertThreshold(value);
                              },
                            ),
                          ),
                          Text('${controller.currencySymbol}20'),
                        ],
                      ),

                      Center(
                        child: Text(
                          'Current: ${controller.currencySymbol}${controller.alertThreshold.toStringAsFixed(2)}',
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              // Days in advance card
              Card(
                margin: const EdgeInsets.only(bottom: 16.0),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Row(
                        children: [
                          Icon(Icons.calendar_today, color: Colors.blue),
                          SizedBox(width: 16),
                          Text(
                            'Days in Advance',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        'Set how many days in advance you want to be notified about low balance.',
                        style: TextStyle(
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Days in advance slider
                      Row(
                        children: [
                          const Text('1 day'),
                          Expanded(
                            child: Slider(
                              value: controller.daysInAdvance.toDouble(),
                              min: 1,
                              max: 99,
                              divisions: 98,
                              label: '${controller.daysInAdvance} days',
                              onChanged: (value) {
                                controller.updateDaysInAdvance(value.toInt());
                              },
                            ),
                          ),
                          const Text('99 days'),
                        ],
                      ),

                      Center(
                        child: Text(
                          'Current: ${controller.daysInAdvance} days',
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              // View all notifications card
              Card(
                margin: const EdgeInsets.only(bottom: 16.0),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Row(
                        children: [
                          Icon(Icons.notifications_active, color: Colors.blue),
                          SizedBox(width: 16),
                          Text(
                            'Notification Center',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        'View all your notifications in one place.',
                        style: TextStyle(
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(height: 16),
                      Center(
                        child: ElevatedButton.icon(
                          icon: const Icon(Icons.notifications),
                          label: const Text('View All Notifications'),
                          onPressed: () {
                            showNotificationDialog(context);
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              // Notification types card
              Card(
                margin: const EdgeInsets.only(bottom: 16.0),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Row(
                        children: [
                          Icon(Icons.notifications, color: Colors.blue),
                          SizedBox(width: 16),
                          Text(
                            'Notification Types',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),

                      // Notifications master toggle
                      SettingsToggle(
                        title: 'Enable Notifications',
                        description: 'Turn on/off all notifications',
                        value: controller.notificationsEnabled,
                        onChanged: (value) {
                          controller.updateNotificationsEnabled(value);
                        },
                        icon: Icons.notifications,
                      ),

                      const Divider(),

                      // Only show these options if notifications are enabled
                      if (controller.notificationsEnabled) ...[
                        // Reminders toggle
                        SettingsToggle(
                          title: 'Reminders',
                          description: 'Remind you to take meter readings',
                          value: controller.remindersEnabled,
                          onChanged: (value) {
                            controller.updateRemindersEnabled(value);
                          },
                          icon: Icons.alarm,
                        ),

                        // Low balance alerts toggle
                        SettingsToggle(
                          title: 'Low Balance Alerts',
                          description:
                              'Alert when you have less than 24 hours of credit remaining',
                          value: controller.lowBalanceAlertsEnabled,
                          onChanged: (value) {
                            controller.updateLowBalanceAlertsEnabled(value);
                          },
                          icon: Icons.money_off,
                        ),

                        // Time to top up alerts toggle
                        SettingsToggle(
                          title: 'Time to Top-up Alerts',
                          description:
                              'Alert when your alert threshold will be reached in your specified days in advance',
                          value: controller.timeToTopUpAlertsEnabled,
                          onChanged: (value) {
                            controller.updateTimeToTopUpAlertsEnabled(value);
                          },
                          icon: Icons.access_time,
                        ),

                        // Invalid record alerts toggle
                        SettingsToggle(
                          title: 'Invalid Record Alerts',
                          description: 'Alert when a record is invalid',
                          value: controller.invalidRecordAlertsEnabled,
                          onChanged: (value) {
                            controller.updateInvalidRecordAlertsEnabled(value);
                          },
                          icon: Icons.error,
                        ),
                      ],
                    ],
                  ),
                ),
              ),

              // Reminder frequency card
              if (controller.notificationsEnabled &&
                  controller.remindersEnabled)
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Row(
                          children: [
                            Icon(Icons.repeat, color: Colors.blue),
                            SizedBox(width: 16),
                            Text(
                              'Reminder Frequency',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        const Text(
                          'How often do you want to be reminded to take meter readings?',
                          style: TextStyle(
                            fontSize: 14,
                          ),
                        ),
                        const SizedBox(height: 16),

                        // Reminder frequency radio buttons
                        RadioListTile<String>(
                          title: const Text('Daily'),
                          value: 'daily',
                          groupValue: controller.reminderFrequency,
                          onChanged: (value) {
                            if (value != null) {
                              controller.updateReminderFrequency(value);
                            }
                          },
                        ),
                        RadioListTile<String>(
                          title: const Text('Weekly'),
                          value: 'weekly',
                          groupValue: controller.reminderFrequency,
                          onChanged: (value) {
                            if (value != null) {
                              controller.updateReminderFrequency(value);
                            }
                          },
                        ),
                        RadioListTile<String>(
                          title: const Text('Bi-weekly'),
                          value: 'bi-weekly',
                          groupValue: controller.reminderFrequency,
                          onChanged: (value) {
                            if (value != null) {
                              controller.updateReminderFrequency(value);
                            }
                          },
                        ),
                        RadioListTile<String>(
                          title: const Text('Monthly'),
                          value: 'monthly',
                          groupValue: controller.reminderFrequency,
                          onChanged: (value) {
                            if (value != null) {
                              controller.updateReminderFrequency(value);
                            }
                          },
                        ),
                      ],
                    ),
                  ),
                ),
            ],
          );
        },
      ),
    );
  }
}
