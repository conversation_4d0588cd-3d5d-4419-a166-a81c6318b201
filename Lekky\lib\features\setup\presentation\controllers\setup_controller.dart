import 'package:flutter/material.dart';
import '../../domain/models/setup_preferences.dart';
import '../../domain/models/date_format.dart';
import '../../data/repositories/preferences_repository.dart';
import '../../../../core/shared/models/theme_mode.dart';
import '../../../../core/shared/models/reminder_frequency.dart';
import '../../../../core/di/service_locator.dart';
import '../../../../core/theme/theme_manager.dart';

/// Controller for the setup screen
class SetupController extends ChangeNotifier {
  /// Repository for preferences
  final PreferencesRepository _repository;

  /// Current preferences
  SetupPreferences preferences = SetupPreferences();

  /// Loading state
  bool isLoading = false;

  /// Error message
  String? error;

  /// Current step in the setup process
  int _currentStep = 0;

  /// Get the current step
  int get currentStep => _currentStep;

  /// Constructor
  SetupController({PreferencesRepository? repository})
      : _repository = repository ?? PreferencesRepository() {
    _loadPreferences();
  }

  /// Load saved preferences
  Future<void> _loadPreferences() async {
    try {
      isLoading = true;
      notifyListeners();

      preferences = await _repository.loadPreferences();

      isLoading = false;
      notifyListeners();
    } catch (e) {
      isLoading = false;
      error = e.toString();
      notifyListeners();
    }
  }

  /// Set date format
  void setDateFormat(DateFormat format) {
    preferences = preferences.copyWith(dateFormat: format);
    notifyListeners();
  }

  /// Set show time with date
  void setShowTimeWithDate(bool show) {
    preferences = preferences.copyWith(showTimeWithDate: show);
    notifyListeners();
  }

  /// Set alert threshold
  void setAlertThreshold(double value) {
    if (value >= 1.00 && value <= 999.99) {
      preferences = preferences.copyWith(alertThreshold: value);
      notifyListeners();
    }
  }

  /// Set days in advance
  void setDaysInAdvance(int days) {
    if (days >= 1 && days <= 99) {
      preferences = preferences.copyWith(daysInAdvance: days);
      notifyListeners();
    }
  }

  /// Set initial meter reading
  void setInitialMeterReading(double? value) {
    preferences = preferences.copyWith(initialMeterReading: value);
    notifyListeners();
  }

  /// Set language
  void setLanguage(String language) {
    preferences = preferences.copyWith(language: language);
    notifyListeners();
  }

  /// Set currency
  void setCurrency(String currency, String symbol) {
    preferences = preferences.copyWith(
      currency: currency,
      currencySymbol: symbol,
    );
    notifyListeners();
  }

  /// Set theme mode
  void setThemeMode(AppThemeMode mode) async {
    preferences = preferences.copyWith(themeMode: mode);

    // Apply theme change immediately
    final themeManager = serviceLocator<ThemeManager>();
    await themeManager.setThemeMode(mode);

    notifyListeners();
  }

  /// Save all preferences
  Future<bool> savePreferences() async {
    try {
      isLoading = true;
      notifyListeners();

      await _repository.savePreferences(preferences);

      isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      isLoading = false;
      error = e.toString();
      notifyListeners();
      return false;
    }
  }

  /// Go to next step
  void nextStep() {
    if (_currentStep < 4) {
      // Assuming 5 steps (0-4)
      _currentStep++;
      notifyListeners();
    }
  }

  /// Go to previous step
  void previousStep() {
    if (_currentStep > 0) {
      _currentStep--;
      notifyListeners();
    }
  }

  /// Go to specific step
  void goToStep(int step) {
    if (step >= 0 && step <= 4) {
      // Assuming 5 steps (0-4)
      _currentStep = step;
      notifyListeners();
    }
  }

  /// Validate current step
  bool validateCurrentStep() {
    switch (_currentStep) {
      case 0: // Date Settings
        return true; // Always valid as we have defaults
      case 1: // Alert Settings
        return preferences.alertThreshold >= 1.00 &&
            preferences.alertThreshold <= 999.99 &&
            preferences.daysInAdvance >= 1 &&
            preferences.daysInAdvance <= 99;
      case 2: // Meter Reading
        return preferences.initialMeterReading == null ||
            preferences.initialMeterReading! >= 0;
      case 3: // Region Settings
        return preferences.language.isNotEmpty &&
            preferences.currency.isNotEmpty &&
            preferences.currencySymbol.isNotEmpty;
      default:
        return true;
    }
  }

  /// Complete setup
  Future<bool> completeSetup() async {
    try {
      isLoading = true;
      notifyListeners();

      await _repository.markSetupCompleted();

      isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      isLoading = false;
      error = e.toString();
      notifyListeners();
      return false;
    }
  }
}
